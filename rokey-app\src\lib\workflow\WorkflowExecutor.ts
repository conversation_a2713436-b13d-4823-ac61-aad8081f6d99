/**
 * Workflow Executor for Manual Build
 * Handles execution of workflows with Memory node integration
 */

import { WorkflowNode } from '@/types/manualBuild';
import { IntelligentBrowsingService } from '../intelligentBrowsing';
import { routerMemoryService } from '../memory/RouterMemoryService';
import { workflowMonitor } from './WorkflowExecutionMonitor';
import { emitWorkflowEvent } from '@/lib/websocket/WorkflowWebSocketServer';
import { errorRecoveryService, ErrorContext } from './ErrorRecoveryService';
import { getFunctionsForTools, FunctionSchema } from './functionSchemas';
import {
  FunctionCallHandler,
  FunctionCall,
  FunctionCallResult,
  hasFunctionCalls,
  extractFunctionCalls,
  formatFunctionCallResults
} from './functionCallHandler';
import crypto from 'crypto';

interface WorkflowExecution {
  id: string;
  userId: string;
  nodes: WorkflowNode[];
  edges: Array<{
    id: string;
    source: string;
    target: string;
    sourceHandle?: string;
    targetHandle?: string;
  }>;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  result?: any;
  error?: string;
}

export class WorkflowExecutor {
  private static instance: WorkflowExecutor;
  private activeExecutions = new Map<string, WorkflowExecution>();
  private executionAttempts = new Map<string, number>(); // Track execution attempts per node
  private maxRetries = 3; // Maximum retries before stopping

  static getInstance(): WorkflowExecutor {
    if (!WorkflowExecutor.instance) {
      WorkflowExecutor.instance = new WorkflowExecutor();
    }
    return WorkflowExecutor.instance;
  }

  /**
   * Execute a workflow with Memory node integration
   */
  async executeWorkflow(
    workflowId: string,
    userId: string,
    nodes: WorkflowNode[],
    edges: Array<{
      id: string;
      source: string;
      target: string;
      sourceHandle?: string;
      targetHandle?: string;
    }>,
    userInput?: string,
    messages?: Array<{ role: string; content: any }>,
    onStreamChunk?: (chunk: string) => void
  ): Promise<any> {
    console.log(`🚀 Starting workflow execution: ${workflowId}`);

    // Clear any previous execution attempts for this workflow
    const workflowAttemptKeys = Array.from(this.executionAttempts.keys()).filter(key => key.startsWith(`${workflowId}-`));
    workflowAttemptKeys.forEach(key => this.executionAttempts.delete(key));

    // Generate unique execution ID
    const executionId = crypto.randomUUID();
    const startTime = Date.now();

    // Emit workflow started event
    emitWorkflowEvent(workflowId, userId, 'workflow_started', {
      executionId,
      totalNodes: nodes.length,
      message: 'Workflow execution started'
    }, executionId);

    // Start monitoring
    await workflowMonitor.startExecution(executionId, workflowId, userId, nodes.length);

    const execution: WorkflowExecution = {
      id: executionId,
      userId,
      nodes,
      edges,
      status: 'running',
      startTime: new Date().toISOString()
    };

    this.activeExecutions.set(executionId, execution);

    try {
      // Step 1: Identify Memory nodes and connect them to other nodes
      await workflowMonitor.updateProgress(
        executionId,
        'system',
        'system',
        'starting',
        'Connecting memory nodes'
      );

      await this.connectMemoryNodes(workflowId, userId, nodes, edges);

      // Step 2: Find entry point (User Request node)
      const entryNode = nodes.find(node => node.type === 'userRequest');
      if (!entryNode) {
        throw new Error('No User Request node found in workflow');
      }

      await workflowMonitor.updateProgress(
        executionId,
        'system',
        'system',
        'completed',
        'Memory nodes connected, starting execution'
      );

      // Step 3: Execute workflow starting from entry point
      const result = await this.executeFromNode(entryNode, nodes, edges, userInput, workflowId, userId, executionId, messages, onStreamChunk);

      execution.status = 'completed';
      execution.endTime = new Date().toISOString();
      execution.result = result;

      // Complete monitoring
      const totalDuration = Date.now() - startTime;
      await workflowMonitor.completeExecution(executionId, result, totalDuration);

      // Emit workflow completed event
      emitWorkflowEvent(workflowId, userId, 'workflow_completed', {
        executionId,
        result,
        duration: totalDuration,
        message: 'Workflow execution completed successfully'
      }, executionId);

      console.log(`✅ Workflow completed: ${workflowId}`);
      return result;

    } catch (error) {
      execution.status = 'failed';
      execution.endTime = new Date().toISOString();
      execution.error = error instanceof Error ? error.message : 'Unknown error';

      // Fail monitoring
      await workflowMonitor.failExecution(
        executionId,
        error instanceof Error ? error.message : 'Unknown error',
        { error, workflowId, userId }
      );

      // Emit workflow failed event
      emitWorkflowEvent(workflowId, userId, 'workflow_failed', {
        executionId,
        error: execution.error,
        message: 'Workflow execution failed'
      }, executionId);

      console.error(`❌ Workflow failed: ${workflowId}`, error);
      throw error;
    }
  }

  /**
   * Connect Memory nodes to their target nodes
   */
  private async connectMemoryNodes(
    workflowId: string,
    userId: string,
    nodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; targetHandle?: string }>
  ): Promise<void> {
    const memoryNodes = nodes.filter(node => node.type === 'memory');

    for (const memoryNode of memoryNodes) {
      // Find nodes that this memory connects to
      const memoryConnections = edges.filter(edge => edge.source === memoryNode.id);

      for (const connection of memoryConnections) {
        const targetNode = nodes.find(node => node.id === connection.target);
        if (!targetNode) continue;

        console.log(`🧠 Connecting Memory "${memoryNode.data.config?.memoryName}" to ${targetNode.type} node`);

        // Connect based on target node type
        switch (targetNode.type) {
          case 'browsing':
            const browsingService = IntelligentBrowsingService.getInstance();
            browsingService.connectMemory(memoryNode.id, workflowId, userId);
            break;

          case 'centralRouter':
            routerMemoryService.connectMemory(memoryNode.id, workflowId, userId);
            break;

          default:
            console.log(`⚠️ Memory connection not implemented for node type: ${targetNode.type}`);
        }
      }
    }
  }

  /**
   * Execute workflow starting from a specific node
   */
  private async executeFromNode(
    currentNode: WorkflowNode,
    allNodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; sourceHandle?: string; targetHandle?: string }>,
    input: any,
    workflowId: string,
    userId: string,
    executionId?: string,
    messages?: Array<{ role: string; content: any }>,
    onStreamChunk?: (chunk: string) => void
  ): Promise<any> {
    console.log(`🔄 Executing node: ${currentNode.type} (${currentNode.id})`);

    // Circuit breaker: Check if this node has been attempted too many times
    const attemptKey = `${workflowId}-${currentNode.id}`;
    const currentAttempts = this.executionAttempts.get(attemptKey) || 0;

    if (currentAttempts >= this.maxRetries) {
      console.error(`🚫 Circuit breaker activated: Node ${currentNode.id} has failed ${currentAttempts} times. Stopping execution.`);
      throw new Error(`Node ${currentNode.id} has exceeded maximum retry attempts (${this.maxRetries}). Execution stopped to prevent infinite loops.`);
    }

    // Increment attempt counter
    this.executionAttempts.set(attemptKey, currentAttempts + 1);

    const nodeStartTime = Date.now();

    // Update monitoring
    if (executionId) {
      await workflowMonitor.updateProgress(
        executionId,
        currentNode.id,
        currentNode.type,
        'starting',
        `Starting execution of ${currentNode.data.label || currentNode.type} node (attempt ${currentAttempts + 1})`
      );
    }

    // Emit node started event
    emitWorkflowEvent(workflowId, userId, 'node_started', {
      nodeId: currentNode.id,
      nodeType: currentNode.type,
      nodeLabel: currentNode.data.label,
      message: `Started executing ${currentNode.type} node`
    }, executionId);

    let result = input;
    let attempt = 1;
    const maxRetries = 3;

    // Execute current node with error recovery
    while (attempt <= maxRetries) {
      try {
        // Execute current node based on its type
        switch (currentNode.type) {
          case 'userRequest':
            result = input; // Pass through user input
            break;

          case 'browsing':
            result = await this.executeBrowsingNode(currentNode, input, workflowId, userId, allNodes, edges);
            break;

          case 'centralRouter':
            result = await this.executeRouterNode(currentNode, input, allNodes, edges);
            break;

          case 'provider':
            result = await this.executeProviderNode(currentNode, input, messages, onStreamChunk, allNodes, edges, workflowId, userId);
            break;

          case 'planner':
            // Planner runs silently - don't stream its planning output to user
            result = await this.executePlannerNode(currentNode, input);
            break;

          case 'memory':
            // Memory nodes don't execute directly - they provide services to other nodes
            result = input;
            break;

          case 'classifier':
            // Classifier runs silently - don't stream its JSON output to user
            result = await this.executeClassifierNode(currentNode, input, allNodes, edges, undefined, messages);
            break;

          case 'tool':
            result = await this.executeToolNode(currentNode, input, allNodes, edges);
            break;

          case 'output':
            result = await this.executeOutputNode(currentNode, input, allNodes, edges);
            break;

          case 'vision':
            result = await this.executeVisionNode(currentNode, input, allNodes, edges, onStreamChunk);
            break;

          case 'roleAgent':
            result = await this.executeRoleAgentNode(currentNode, input, allNodes, edges);
            break;

          default:
            console.log(`⚠️ Execution not implemented for node type: ${currentNode.type}`);
            result = input;
        }

        // If we reach here, execution was successful
        break;

      } catch (error) {
        console.error(`❌ Node execution failed (attempt ${attempt}/${maxRetries}):`, error);

        // Create error context
        const errorContext: ErrorContext = {
          nodeId: currentNode.id,
          nodeType: currentNode.type,
          nodeLabel: currentNode.data.label,
          error: error instanceof Error ? error : new Error(String(error)),
          attempt,
          maxRetries,
          workflowId,
          userId,
          executionId,
          input
        };

        // Attempt error recovery
        const recoveryResult = await errorRecoveryService.recoverFromError(errorContext);

        if (recoveryResult.success) {
          console.log(`✅ Error recovery successful: ${recoveryResult.message}`);
          result = recoveryResult.result;
          break;
        } else if (!recoveryResult.shouldContinue) {
          // Recovery failed and we should not continue
          throw error;
        }

        // Increment attempt for retry
        attempt++;

        // If this was the last attempt, throw the error
        if (attempt > maxRetries) {
          throw error;
        }
      }
    }

    // Find next nodes to execute
    let nextEdges = edges.filter(edge => edge.source === currentNode.id);

    // Special handling for central router
    if (currentNode.type === 'centralRouter') {
      // Check if router wants to route to output (task complete)
      if (result?.routeToOutput) {
        console.log(`📤 Central router routing to output - task complete`);

        // Find the output node
        const outputNode = allNodes.find(node => node.type === 'output');
        if (outputNode) {
          // Update monitoring for completed node
          if (executionId) {
            const nodeDuration = Date.now() - nodeStartTime;
            await workflowMonitor.updateProgress(
              executionId,
              currentNode.id,
              currentNode.type,
              'completed',
              `Completed ${currentNode.data.label || currentNode.type} node - routing to output`,
              { result },
              nodeDuration
            );
          }

          // Emit node completed event
          const nodeDuration = Date.now() - nodeStartTime;
          emitWorkflowEvent(workflowId, userId, 'node_completed', {
            nodeId: currentNode.id,
            nodeType: currentNode.type,
            nodeLabel: currentNode.data.label,
            duration: nodeDuration,
            result: typeof result === 'object' ? JSON.stringify(result) : result,
            message: `Completed ${currentNode.type} node in ${nodeDuration}ms - routing to output`
          }, executionId);

          // Execute the output node with the AI response
          return await this.executeFromNode(outputNode, allNodes, edges, result, workflowId, userId, executionId, messages, onStreamChunk);
        }
      }
      // Check if router wants to route to AI provider
      else if (result?.selectedProvider) {
        console.log(`🎯 Central router selected provider: ${result.selectedProvider}`);

        // Find the AI provider node with the selected provider
        const selectedProviderNode = allNodes.find(node =>
          node.type === 'provider' &&
          node.data.config?.providerId === result.selectedProvider
        );

        if (selectedProviderNode) {
          console.log(`🚀 Routing to AI provider: ${selectedProviderNode.id}`);

          // Update monitoring for completed node
          if (executionId) {
            const nodeDuration = Date.now() - nodeStartTime;
            await workflowMonitor.updateProgress(
              executionId,
              currentNode.id,
              currentNode.type,
              'completed',
              `Completed ${currentNode.data.label || currentNode.type} node - routing to ${result.selectedProvider}`,
              { result },
              nodeDuration
            );
          }

          // Emit node completed event
          const nodeDuration = Date.now() - nodeStartTime;
          emitWorkflowEvent(workflowId, userId, 'node_completed', {
            nodeId: currentNode.id,
            nodeType: currentNode.type,
            nodeLabel: currentNode.data.label,
            duration: nodeDuration,
            result: typeof result === 'object' ? JSON.stringify(result) : result,
            message: `Completed ${currentNode.type} node in ${nodeDuration}ms - routing to ${result.selectedProvider}`
          }, executionId);

          // Execute the selected AI provider with the full result (including classification data)
          return await this.executeFromNode(selectedProviderNode, allNodes, edges, result, workflowId, userId, executionId, messages, onStreamChunk);
        } else {
          console.warn(`⚠️ Selected provider ${result.selectedProvider} not found in workflow`);
        }
      }
    }

    if (nextEdges.length === 0) {
      // End of workflow
      return result;
    }

    // Update monitoring for completed node
    if (executionId) {
      const nodeDuration = Date.now() - nodeStartTime;
      await workflowMonitor.updateProgress(
        executionId,
        currentNode.id,
        currentNode.type,
        'completed',
        `Completed ${currentNode.data.label || currentNode.type} node`,
        { result },
        nodeDuration
      );
    }

    // Emit node completed event
    const nodeDuration = Date.now() - nodeStartTime;
    emitWorkflowEvent(workflowId, userId, 'node_completed', {
      nodeId: currentNode.id,
      nodeType: currentNode.type,
      nodeLabel: currentNode.data.label,
      duration: nodeDuration,
      result: typeof result === 'object' ? JSON.stringify(result) : result,
      message: `Completed ${currentNode.type} node in ${nodeDuration}ms`
    }, executionId);

    // Reset attempt counter on successful execution
    this.executionAttempts.delete(attemptKey);

    // Execute next nodes (for now, just take the first one - could be enhanced for parallel execution)
    const nextEdge = nextEdges[0];
    const nextNode = allNodes.find(node => node.id === nextEdge.target);

    if (nextNode) {
      return await this.executeFromNode(nextNode, allNodes, edges, result, workflowId, userId, executionId, messages, onStreamChunk);
    }

    return result;
  }

  /**
   * Execute a Browsing node
   */
  private async executeBrowsingNode(
    node: WorkflowNode,
    input: any,
    workflowId: string,
    userId: string,
    allNodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; sourceHandle?: string; targetHandle?: string }>
  ): Promise<any> {
    console.log(`🌐 Executing Browsing node: ${node.id}`);

    const browsingService = IntelligentBrowsingService.getInstance();

    // Find connected Planner node
    const plannerConnection = edges.find(edge =>
      edge.target === node.id && edge.targetHandle === 'planner'
    );
    const plannerNode = plannerConnection ?
      allNodes.find(n => n.id === plannerConnection.source) : null;

    // Find connected AI Provider node (for planner execution)
    const aiProviderConnection = edges.find(edge =>
      edge.source === node.id && edge.sourceHandle === 'output'
    );
    const aiProviderNode = aiProviderConnection ?
      allNodes.find(n => n.id === aiProviderConnection.target) : null;

    // Create a browsing plan from the input
    const plan = {
      id: `plan_${Date.now()}`,
      task: typeof input === 'string' ? input : 'Browse the web for information',
      subtasks: [
        {
          id: 'search_1',
          type: 'search' as const,
          description: 'Search for relevant information',
          target: typeof input === 'string' ? input : 'general search',
          status: 'pending' as const
        }
      ],
      estimatedTime: 5,
      priority: 'medium' as const
    };

    // Create memory for this task
    const memory = browsingService.createTaskMemory(plan.id);

    // Execute the browsing plan with planner and AI provider integration
    const { result } = await browsingService.executeBrowsingPlan(
      plan,
      memory,
      node.data.config,
      plannerNode?.id,
      aiProviderNode?.data.config
    );

    console.log(`✅ Browsing node completed: ${node.id}`);
    return result;
  }

  /**
   * Execute a Central Router node
   */
  private async executeRouterNode(
    node: WorkflowNode,
    input: any,
    allNodes: WorkflowNode[],
    edges: Array<{ source: string; target: string; targetHandle?: string }>
  ): Promise<any> {
    // Check if this is a response from an AI provider (indicating task completion)
    if (input && typeof input === 'object' && input.provider && input.response) {
      console.log(`🎯 Central router received AI response - task appears complete`);

      // Find connected output node
      const outputConnections = edges.filter(edge =>
        edge.source === node.id &&
        allNodes.find(n => n.id === edge.target)?.type === 'output'
      );

      if (outputConnections.length > 0) {
        console.log(`📤 Routing to output node for final response`);
        return {
          ...input,
          routeToOutput: true,
          isTaskComplete: true
        };
      }
    }

    // Extract the original user message from the input (preserve it through classification)
    let originalUserMessage = '';
    if (typeof input === 'string') {
      originalUserMessage = input;
    } else if (input && typeof input === 'object') {
      // Look for the original user message in various possible locations
      originalUserMessage = input.userMessage ||
                           input.message ||
                           input.content ||
                           input.prompt ||
                           input.input ||
                           // Handle the case where user input is stored as indexed properties (0, 1, etc.)
                           (typeof input['0'] === 'string' && typeof input['1'] === 'string' ?
                            Object.keys(input).filter(k => !isNaN(Number(k))).map(k => input[k]).join('') :
                            '');
    }

    console.log(`🎯 Central router extracted user message: "${originalUserMessage}"`);

    if (!originalUserMessage.trim()) {
      console.warn('No clear user message found, using full input for routing');
      originalUserMessage = typeof input === 'string' ? input : JSON.stringify(input);
    }

    // Find connected AI providers
    const providerConnections = edges.filter(edge =>
      edge.target === node.id && edge.targetHandle === 'providers'
    );

    const availableProviders = providerConnections
      .map(edge => allNodes.find(n => n.id === edge.source))
      .filter(n => n?.type === 'provider')
      .map(n => n!.data.config?.providerId)
      .filter(Boolean);

    // Find connected tools
    const toolConnections = edges.filter(edge =>
      edge.target === node.id && edge.targetHandle === 'tools'
    );

    const availableTools = toolConnections
      .map(edge => allNodes.find(n => n.id === edge.source))
      .filter(n => n?.type === 'tool')
      .map(n => ({
        id: n!.id,
        type: n!.data.config?.toolType,
        config: n!.data.config,
        status: n!.data.config?.connectionStatus || 'disconnected'
      }))
      .filter(Boolean);

    // Find connected memory
    const memoryConnections = edges.filter(edge =>
      edge.target === node.id && edge.targetHandle === 'memory'
    );

    const memoryNodes = memoryConnections
      .map(edge => allNodes.find(n => n.id === edge.source))
      .filter(n => n?.type === 'memory');

    if (availableProviders.length === 0) {
      throw new Error('No AI providers connected to router');
    }

    // Get available provider details for intelligent routing
    const providerDetails = providerConnections
      .map(edge => allNodes.find(n => n.id === edge.source))
      .filter(n => n?.type === 'provider')
      .map(n => {
        // Find connected roles for this provider
        const connectedRoles = edges
          .filter(e => e.target === n!.id && e.targetHandle === 'role')
          .map(e => {
            const roleNode = allNodes.find(rn => rn.id === e.source);
            if (roleNode && roleNode.type === 'roleAgent') {
              const roleConfig = roleNode.data.config as any;
              return {
                id: roleNode.id,
                name: roleConfig?.roleName || roleNode.data.label || 'Unknown Role',
                description: roleConfig?.roleDescription || roleConfig?.customPrompt || 'General purpose AI assistant',
                type: roleConfig?.roleType || 'custom'
              };
            }
            return null;
          })
          .filter(Boolean);

        return {
          id: n!.id,
          providerId: n!.data.config?.providerId,
          modelId: n!.data.config?.modelId,
          roles: connectedRoles,
          capabilities: n!.data.config?.capabilities || [],
          // Legacy role field for backward compatibility
          role: connectedRoles.length > 0 ? connectedRoles.map(r => r.name).join(', ') : 'general_chat'
        };
      });

    // Debug log provider details
    console.log('🔧 Provider details for routing:', JSON.stringify(providerDetails, null, 2));

    // Use Gemini AI for intelligent routing decision
    const recommendation = await this.getIntelligentRoutingRecommendation(
      originalUserMessage,
      input.classification || {},
      providerDetails,
      availableTools
    );

    console.log(`🎯 Router recommendation: ${recommendation.recommendedProvider} (${recommendation.confidence * 100}% confidence)`);
    console.log(`📝 Reason: ${recommendation.reason}`);
    console.log(`🔧 Available tools: ${availableTools.map(t => t.type).join(', ')}`);
    console.log(`🧠 Memory nodes: ${memoryNodes.length}`);

    // Record the routing decision
    const startTime = Date.now();

    try {
      // Find the specific provider node that was recommended
      const selectedProviderNode = providerConnections
        .map(edge => allNodes.find(n => n.id === edge.source))
        .filter(n => n?.type === 'provider')
        .find(n => n!.data.config?.providerId === recommendation.recommendedProvider);

      if (!selectedProviderNode) {
        throw new Error(`Recommended provider ${recommendation.recommendedProvider} not found in connected providers`);
      }

      // Enhanced routing result with tools and memory context
      const result = {
        selectedProvider: recommendation.recommendedProvider,
        selectedProviderId: selectedProviderNode.id, // The actual node ID to route to
        confidence: recommendation.confidence,
        reason: recommendation.reason,
        // Pass the original user message to the AI provider, not the classification data
        userMessage: originalUserMessage,
        // Keep classification data for context but don't pass it as the main input
        classification: input.classification || null,
        availableTools: availableTools.filter(t => t.status === 'connected'),
        memoryContext: memoryNodes.length > 0,
        routingContext: {
          totalProviders: availableProviders.length,
          totalTools: availableTools.length,
          connectedTools: availableTools.filter(t => t.status === 'connected').length,
          hasMemory: memoryNodes.length > 0
        }
      };

      // Record successful routing using the original user message
      await routerMemoryService.recordRoutingDecision(
        originalUserMessage,
        recommendation.recommendedProvider,
        recommendation.reason,
        Date.now() - startTime,
        true
      );

      console.log(`🎯 Central router selected provider: ${recommendation.recommendedProvider}`);
      console.log(`🚀 Routing to AI provider: ${result.selectedProviderId}`);

      return result;
    } catch (error) {
      // Record failed routing using the original user message
      await routerMemoryService.recordRoutingDecision(
        originalUserMessage,
        recommendation.recommendedProvider,
        recommendation.reason,
        Date.now() - startTime,
        false
      );
      throw error;
    }
  }

  /**
   * Detect workflow capabilities based on connected nodes
   */
  private detectWorkflowCapabilities(allNodes: WorkflowNode[]): { tools: string[], hasBrowsing: boolean, capabilities: string } {
    const toolNodes = allNodes.filter(node => node.type === 'tool');
    const browsingNodes = allNodes.filter(node => node.type === 'browsing');

    const tools = toolNodes.map(node => {
      const toolType = node.data.config?.toolType || 'unknown';
      switch (toolType) {
        case 'google_drive': return 'Google Drive';
        case 'google_docs': return 'Google Docs';
        case 'google_sheets': return 'Google Sheets';
        case 'notion': return 'Notion';
        case 'calendar': return 'Google Calendar';
        case 'gmail': return 'Gmail';
        case 'youtube': return 'YouTube';
        case 'supabase': return 'Database operations';
        default: return toolType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      }
    }).filter(tool => tool !== 'unknown'); // Filter out unconfigured tools

    const hasBrowsing = browsingNodes.length > 0;

    // Generate capability description (subtle instructions for when asked)
    let capabilities = '';
    if (tools.length > 0 || hasBrowsing) {
      capabilities = 'Available capabilities in this workflow:\n';

      if (hasBrowsing) {
        capabilities += '- Web browsing and internet access\n';
      }

      if (tools.length > 0) {
        capabilities += `- Tool integrations: ${tools.join(', ')}\n`;
      }

      capabilities += '\nOnly mention these capabilities when specifically asked about them. Do not volunteer this information unless the user asks about what you can do or requests help with these specific tools/browsing.';
    }

    return { tools, hasBrowsing, capabilities };
  }

  /**
   * Execute tools and get AI response with tool results
   */
  private async executeToolsAndGetResponse(
    toolTypes: string[],
    userMessage: string,
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[],
    conversationMessages: Array<{ role: string; content: string }>,
    providerId: string,
    modelId: string,
    apiKey: string,
    onStreamChunk?: (chunk: string) => void,
    workflowId?: string,
    userId?: string
  ): Promise<string> {
    console.log(`🔧 executeToolsAndGetResponse called with tools:`, toolTypes);
    // For content creation tools, we need to generate content first
    // Check this BEFORE executing any tools
    const lowerMessage = userMessage.toLowerCase();
    const contentCreationTools = ['google_docs', 'notion'];
    const hasContentCreationTool = toolTypes.some(tool => contentCreationTools.includes(tool));

    // Only trigger content-first workflow for CREATIVE content requests, not simple document creation
    const isContentCreationRequest = hasContentCreationTool && (
      (lowerMessage.includes('story') || lowerMessage.includes('write a story') || lowerMessage.includes('create a story')) ||
      (lowerMessage.includes('write') && (lowerMessage.includes('article') || lowerMessage.includes('essay') || lowerMessage.includes('blog') || lowerMessage.includes('content'))) ||
      (lowerMessage.includes('generate') && (lowerMessage.includes('content') || lowerMessage.includes('text') || lowerMessage.includes('article'))) ||
      (lowerMessage.includes('compose') && (lowerMessage.includes('letter') || lowerMessage.includes('email') || lowerMessage.includes('message'))) ||
      (lowerMessage.includes('draft') && (lowerMessage.includes('content') || lowerMessage.includes('article') || lowerMessage.includes('post')))
    );

    if (isContentCreationRequest) {
      console.log('🎨 Detected content creation request - using content-first workflow');
      console.log('🚫 Skipping regular tool execution to prevent duplicates');
      return await this.executeContentFirstWorkflow(conversationMessages, providerId, modelId, apiKey, toolTypes, [], onStreamChunk, workflowId, userId);
    }

    // For simple document creation, data manipulation, or tool usage requests,
    // use the unified function calling system
    console.log('🔧 Using unified function calling system');

    // Get available functions for connected tools
    const connectedTools = this.getConnectedToolTypes(allNodes, edges);
    const availableFunctions = getFunctionsForTools(connectedTools);

    console.log(`🔧 Available functions for AI: ${availableFunctions.map(f => f.name).join(', ')}`);

    // Make AI API call with function calling support
    if (providerId === 'google' && apiKey) {
      return await this.callGeminiAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
    } else if (providerId === 'openai' && apiKey) {
      return await this.callOpenAIAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
    } else if (providerId === 'anthropic' && apiKey) {
      return await this.callAnthropicAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
    } else if (providerId === 'deepseek' && apiKey) {
      return await this.callDeepSeekAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
    } else if (providerId === 'xai' && apiKey) {
      return await this.callXAIAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
    } else if (providerId === 'openrouter' && apiKey) {
      return await this.callOpenRouterAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
    } else {
      return `I apologize, but I don't have access to the ${providerId} API or the API key is missing. Please check your configuration.`;
    }
  }

  /**
   * Execute Google Docs tool
   */
  private async executeGoogleDocsTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    const { ToolExecutor } = await import('./toolExecutor');

    if (!userId) {
      return {
        success: false,
        error: 'User ID is required for tool execution'
      };
    }

    const context = {
      userInput: userMessage,
      workflowId: workflowId || 'unknown-workflow',
      nodeId: toolNode.id,
      userId: userId
    };

    // Parse the user message to determine what to do with Google Docs
    if (userMessage.toLowerCase().includes('write') || userMessage.toLowerCase().includes('create') || userMessage.toLowerCase().includes('story')) {
      // Create a new document with the story
      const title = this.extractDocumentTitle(userMessage) || 'AI Generated Story';

      try {
        // Import the Google Docs API
        const { GoogleDocsAPI } = await import('./toolImplementations');

        // First create the document (increased timeout for Google API)
        console.log(`🔧 Creating Google Doc with title: "${title}"`);
        const createResult = await GoogleDocsAPI.createDocument(userId, { title }, 60000);

        if (createResult.success && createResult.document) {
          const documentId = createResult.document.documentId;
          const documentUrl = `https://docs.google.com/document/d/${documentId}/edit`;

          // Generate story content
          const storyContent = this.generateStoryContent(userMessage);

          // Update the document with the story content
          const updateRequests = [
            {
              insertText: {
                location: {
                  index: 1
                },
                text: storyContent
              }
            }
          ];

          const updateResult = await GoogleDocsAPI.updateDocument(userId, {
            documentId,
            requests: updateRequests
          }, 60000);

          return {
            success: true,
            document: createResult.document,
            documentUrl,
            title,
            content: storyContent,
            message: `Successfully created and populated Google Doc: "${title}"\nDocument URL: ${documentUrl}`
          };
        }

        return createResult;
      } catch (error) {
        console.error('Error executing Google Docs tool:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    // Default: create an empty document
    try {
      const { GoogleDocsAPI } = await import('./toolImplementations');
      const title = this.extractDocumentTitle(userMessage) || 'New Document';
      return await GoogleDocsAPI.createDocument(userId, { title }, 60000);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Extract document title from user message
   */
  private extractDocumentTitle(userMessage: string): string | null {
    // Enhanced title extraction logic with multiple patterns
    const titlePatterns = [
      // Quoted titles with various punctuation
      /title[:\s]+"([^"]+)"/i,
      /called[:\s]+"([^"]+)"/i,
      /named[:\s]+"([^"]+)"/i,
      /title[:\s]+'([^']+)'/i,
      /called[:\s]+'([^']+)'/i,
      /named[:\s]+'([^']+)'/i,

      // Unquoted titles (more flexible)
      /(?:create|make|new)\s+(?:a\s+)?document\s+(?:called|named|titled)\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,
      /(?:document|doc)\s+(?:called|named|titled)\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,
      /(?:title|name):\s*['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,

      // Simple patterns for common cases
      /called\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,
      /named\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,
      /titled\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i
    ];

    for (const pattern of titlePatterns) {
      const match = userMessage.match(pattern);
      if (match && match[1]) {
        // Clean up the extracted title
        let title = match[1].trim();

        // Remove trailing punctuation and quotes
        title = title.replace(/['".,;:!?]+$/, '');

        // Only return if we have a meaningful title
        if (title.length > 0 && title.length < 200) {
          return title;
        }
      }
    }

    return null;
  }

  /**
   * Extract title from AI-generated story content
   */
  private extractTitleFromStory(storyContent: string): string | null {
    // Look for title patterns in the story content
    const lines = storyContent.split('\n');

    // Check first few lines for title patterns
    for (let i = 0; i < Math.min(5, lines.length); i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // Look for markdown title (# Title)
      const markdownMatch = line.match(/^#+\s*(.+)$/);
      if (markdownMatch) {
        return markdownMatch[1].trim();
      }

      // Look for title-like patterns (first substantial line)
      if (line.length > 10 && line.length < 100) {
        // Check if it looks like a title (no periods, proper case)
        if (!line.includes('.') && /^[A-Z]/.test(line)) {
          return line;
        }
      }
    }

    return null;
  }

  /**
   * Create smart chunks for streaming that respect word boundaries and formatting
   */
  private createSmartChunks(text: string, targetSize: number): string[] {
    const chunks: string[] = [];
    let currentChunk = '';

    // Split text into words while preserving whitespace and special characters
    const tokens = text.split(/(\s+|[^\w\s])/);

    for (const token of tokens) {
      // If adding this token would exceed target size and we have content
      if (currentChunk.length + token.length > targetSize && currentChunk.trim()) {
        // Don't break in the middle of URLs or formatted text
        if (currentChunk.includes('http') && !currentChunk.includes(' ')) {
          // Continue building URL
          currentChunk += token;
        } else if (currentChunk.includes('**') && (currentChunk.match(/\*\*/g) || []).length % 2 === 1) {
          // Continue building bold text
          currentChunk += token;
        } else {
          // Safe to break here
          chunks.push(currentChunk);
          currentChunk = token;
        }
      } else {
        currentChunk += token;
      }
    }

    // Add remaining content
    if (currentChunk) {
      chunks.push(currentChunk);
    }

    return chunks.filter(chunk => chunk.trim()); // Remove empty chunks
  }

  /**
   * Create formatted document requests for Google Docs
   */
  private createFormattedDocumentRequests(storyContent: string): any[] {
    const requests = [];
    let currentIndex = 1;

    // Split content into paragraphs
    const paragraphs = storyContent.split('\n').filter(p => p.trim());

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i].trim();

      if (!paragraph) continue;

      // Check if this looks like a title (first paragraph or markdown title)
      const isTitle = i === 0 || paragraph.startsWith('#');
      const cleanText = paragraph.replace(/^#+\s*/, '') + '\n\n';

      // Insert the text
      requests.push({
        insertText: {
          location: { index: currentIndex },
          text: cleanText
        }
      });

      // Format as title if needed
      if (isTitle) {
        requests.push({
          updateTextStyle: {
            range: {
              startIndex: currentIndex,
              endIndex: currentIndex + cleanText.length - 2 // Exclude the newlines
            },
            textStyle: {
              bold: true,
              fontSize: {
                magnitude: 18,
                unit: 'PT'
              }
            },
            fields: 'bold,fontSize'
          }
        });
      } else {
        // Regular paragraph formatting
        requests.push({
          updateTextStyle: {
            range: {
              startIndex: currentIndex,
              endIndex: currentIndex + cleanText.length - 2
            },
            textStyle: {
              fontSize: {
                magnitude: 11,
                unit: 'PT'
              }
            },
            fields: 'fontSize'
          }
        });
      }

      currentIndex += cleanText.length;
    }

    return requests;
  }

  /**
   * Execute workflow where AI generates content first, then tools use that content
   */
  private async executeContentFirstWorkflow(
    conversationMessages: any[],
    providerId: string,
    modelId: string,
    apiKey: string,
    toolTypes: string[],
    toolResults: any[],
    onStreamChunk?: (chunk: string) => void,
    workflowId?: string,
    userId?: string
  ): Promise<string> {
    try {
      // Step 1: Generate content with AI first
      const contentGenerationPrompt = [
        ...conversationMessages,
        {
          role: 'system',
          content: `You are generating content that will be added to a Google Document.

Please generate a complete, well-formatted story based on the user's request.

FORMATTING REQUIREMENTS:
- Start with a compelling title formatted as a markdown heading (# Title)
- Use proper paragraph breaks (separate paragraphs with blank lines)
- Create engaging, well-developed characters and plot
- Include descriptive, immersive language
- End with a satisfying conclusion

STRUCTURE EXAMPLE:
# The Story Title

First paragraph of the story with engaging opening...

Second paragraph continuing the narrative...

Third paragraph developing the plot...

And so on until a satisfying conclusion.

CRITICAL FORMATTING RULES:
- NEVER write the text "[blank line]" - use actual empty lines instead
- NEVER write placeholder text like "[blank line]" or "[empty line]"
- Start with # followed by the title (example: # The Adventure Begins)
- Separate paragraphs with actual blank lines (press Enter twice)
- Generate ONLY the story content - no meta-commentary about documents or tools

EXAMPLE OUTPUT:
# The Mysterious Forest

Sarah stepped into the ancient woods, her heart pounding with anticipation.

The trees whispered secrets in a language she couldn't understand, their branches reaching toward her like gnarled fingers.

As she ventured deeper, the path began to glow with an otherworldly light.`
        }
      ];

      console.log('🎨 Generating story content with AI...');

      let generatedContent = '';
      const contentOnStreamChunk = (chunk: string) => {
        generatedContent += chunk;
        // Don't stream to user yet - we'll stream the final response
      };

      // Generate content with AI
      let aiResponse = '';
      if (providerId === 'google' && apiKey) {
        aiResponse = await this.callGeminiAPI(contentGenerationPrompt, modelId, apiKey, contentOnStreamChunk);
      } else if (providerId === 'openai' && apiKey) {
        aiResponse = await this.callOpenAIAPI(contentGenerationPrompt, modelId, apiKey, contentOnStreamChunk);
      }

      let storyContent = generatedContent || aiResponse;

      // Clean up any placeholder text that might have been generated
      storyContent = storyContent
        .replace(/\[blank line\]/g, '')
        .replace(/\[empty line\]/g, '')
        .replace(/\n\s*\n\s*\n/g, '\n\n') // Normalize multiple blank lines to double
        .trim();

      console.log('✅ Story content generated and cleaned, length:', storyContent.length);

      // Step 2: Create documents/pages with the generated content
      let documentResults: any[] = [];

      // Handle Google Docs
      if (toolTypes.includes('google_docs') && userId) {
        console.log('📄 Creating Google Doc with AI-generated content...');

        const { GoogleDocsAPI } = await import('./toolImplementations');
        const title = this.extractTitleFromStory(storyContent) || 'AI Generated Story';

        const createResult = await GoogleDocsAPI.createDocument(userId, { title }, 60000);

        if (createResult.success && createResult.document) {
          const documentId = createResult.document.documentId;
          const documentUrl = `https://docs.google.com/document/d/${documentId}/edit`;

          const updateRequests = this.createFormattedDocumentRequests(storyContent);
          await GoogleDocsAPI.updateDocument(userId, {
            documentId,
            requests: updateRequests
          }, 60000);

          documentResults.push({
            type: 'Google Docs',
            title,
            url: documentUrl,
            success: true
          });
        }
      }

      // Handle Notion
      if (toolTypes.includes('notion') && userId) {
        console.log('📄 Creating Notion page with AI-generated content...');

        try {
          const { NotionAPI } = await import('./toolImplementations2');
          const title = this.extractTitleFromStory(storyContent) || 'AI Generated Story';

          // Create Notion page (this is a simplified implementation)
          const createResult = await NotionAPI.createPage(userId, {
            parent: { type: 'page_id', page_id: 'default' }, // This would need proper parent configuration
            properties: {
              title: {
                title: [{ text: { content: title } }]
              }
            },
            children: [
              {
                object: 'block',
                type: 'paragraph',
                paragraph: {
                  rich_text: [{ text: { content: storyContent } }]
                }
              }
            ]
          }, 60000);

          if (createResult.success) {
            documentResults.push({
              type: 'Notion',
              title,
              url: createResult.page?.url || 'Notion page created',
              success: true
            });
          }
        } catch (error) {
          console.error('Error creating Notion page:', error);
          documentResults.push({
            type: 'Notion',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Step 3: Generate final response with all document links
      let documentsSection = '';
      if (documentResults.length > 0) {
        documentsSection = '\n\n---\n\n';

        const successfulDocs = documentResults.filter(doc => doc.success);
        const failedDocs = documentResults.filter(doc => !doc.success);

        if (successfulDocs.length > 0) {
          documentsSection += '✅ **Documents Created Successfully!**\n\n';
          successfulDocs.forEach(doc => {
            documentsSection += `📄 **${doc.type}:** ${doc.title}\n🔗 **Link:** ${doc.url}\n\n`;
          });
        }

        if (failedDocs.length > 0) {
          documentsSection += '⚠️ **Some documents could not be created:**\n\n';
          failedDocs.forEach(doc => {
            documentsSection += `❌ **${doc.type}:** ${doc.error}\n\n`;
          });
        }

        documentsSection += 'Your content has been saved and is ready for editing and sharing!';
      }

      const finalResponse = `${storyContent}${documentsSection}`;

      // Stream the final response with smart chunking
      if (onStreamChunk) {
        const chunks = this.createSmartChunks(finalResponse, 15);
        for (const chunk of chunks) {
          onStreamChunk(chunk);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      return finalResponse;

    } catch (error) {
      console.error('❌ Error in content-first workflow:', error);

      const errorResponse = `I encountered an error while generating your content and creating the documents. Let me try a different approach.

Error: ${error instanceof Error ? error.message : 'Unknown error'}`;

      if (onStreamChunk) {
        const chunks = this.createSmartChunks(errorResponse, 15);
        for (const chunk of chunks) {
          onStreamChunk(chunk);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      return errorResponse;
    }
  }

  /**
   * Generate story content based on user request
   * TODO: This should be replaced with AI-generated content
   */
  private generateStoryContent(userMessage: string): string {
    // TEMPORARY: This is a placeholder until we implement AI-first content generation
    // The real solution is to have the AI generate the story first, then pass it to the tool

    return `Document created by RouKey AI Assistant

This document was created in response to your request: "${userMessage}"

The AI will generate your story content and it will appear in your chat response.
This Google Document serves as a workspace where you can edit and expand on the AI-generated content.

---
Generated by RouKey AI Assistant
Created on: ${new Date().toLocaleDateString()}
Document ID: Available in the URL above`;
  }

  /**
   * Execute Google Drive tool
   */
  private async executeGoogleDriveTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    // Placeholder implementation
    return {
      success: false,
      error: 'Google Drive tool execution not yet implemented'
    };
  }

  /**
   * Execute Google Sheets tool
   */
  private async executeGoogleSheetsTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    const { ToolExecutor } = await import('./toolExecutor');

    if (!userId) {
      return {
        success: false,
        error: 'User ID is required for tool execution'
      };
    }

    const context = {
      userInput: userMessage,
      workflowId: workflowId || 'unknown-workflow',
      nodeId: toolNode.id,
      userId: userId
    };

    try {
      const result = await ToolExecutor.executeGoogleSheets(toolNode.data.config as any, context);
      return {
        success: true,
        result: result.data,
        message: `Google Sheets operation completed successfully`
      };
    } catch (error) {
      console.error('Error executing Google Sheets tool:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute Gmail tool
   */
  private async executeGmailTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    const { ToolExecutor } = await import('./toolExecutor');

    if (!userId) {
      return {
        success: false,
        error: 'User ID is required for tool execution'
      };
    }

    const context = {
      userInput: userMessage,
      workflowId: workflowId || 'unknown-workflow',
      nodeId: toolNode.id,
      userId: userId
    };

    try {
      const result = await ToolExecutor.executeGmail(toolNode.data.config as any, context);
      return {
        success: true,
        result: result.data,
        message: `Gmail operation completed successfully`
      };
    } catch (error) {
      console.error('Error executing Gmail tool:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute Google Calendar tool
   */
  private async executeGoogleCalendarTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    const { ToolExecutor } = await import('./toolExecutor');

    if (!userId) {
      return {
        success: false,
        error: 'User ID is required for tool execution'
      };
    }

    const context = {
      userInput: userMessage,
      workflowId: workflowId || 'unknown-workflow',
      nodeId: toolNode.id,
      userId: userId
    };

    try {
      const result = await ToolExecutor.executeGoogleCalendar(toolNode.data.config as any, context);
      return {
        success: true,
        result: result.data,
        message: `Google Calendar operation completed successfully`
      };
    } catch (error) {
      console.error('Error executing Google Calendar tool:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute YouTube tool
   */
  private async executeYouTubeTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    const { ToolExecutor } = await import('./toolExecutor');

    if (!userId) {
      return {
        success: false,
        error: 'User ID is required for tool execution'
      };
    }

    const context = {
      userInput: userMessage,
      workflowId: workflowId || 'unknown-workflow',
      nodeId: toolNode.id,
      userId: userId
    };

    try {
      const result = await ToolExecutor.executeYouTube(toolNode.data.config as any, context);
      return {
        success: true,
        result: result.data,
        message: `YouTube operation completed successfully`
      };
    } catch (error) {
      console.error('Error executing YouTube tool:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute Notion tool
   */
  private async executeNotionTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    const { ToolExecutor } = await import('./toolExecutor');

    if (!userId) {
      return {
        success: false,
        error: 'User ID is required for tool execution'
      };
    }

    const context = {
      userInput: userMessage,
      workflowId: workflowId || 'unknown-workflow',
      nodeId: toolNode.id,
      userId: userId
    };

    try {
      const result = await ToolExecutor.executeNotion(toolNode.data.config as any, context);
      return {
        success: true,
        result: result.data,
        message: `Notion operation completed successfully`
      };
    } catch (error) {
      console.error('Error executing Notion tool:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute Supabase tool
   */
  private async executeSupabaseTool(userMessage: string, toolNode: WorkflowNode, workflowId?: string, userId?: string): Promise<any> {
    const { ToolExecutor } = await import('./toolExecutor');

    if (!userId) {
      return {
        success: false,
        error: 'User ID is required for tool execution'
      };
    }

    const context = {
      userInput: userMessage,
      workflowId: workflowId || 'unknown-workflow',
      nodeId: toolNode.id,
      userId: userId
    };

    try {
      const result = await ToolExecutor.executeSupabase(toolNode.data.config as any, context);
      return {
        success: true,
        result: result.data,
        message: `Supabase operation completed successfully`
      };
    } catch (error) {
      console.error('Error executing Supabase tool:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute a Provider node
   */
  private async executeProviderNode(node: WorkflowNode, input: any, messages?: Array<{ role: string; content: any }>, onStreamChunk?: (chunk: string) => void, allNodes?: WorkflowNode[], edges?: WorkflowEdge[], workflowId?: string, userId?: string): Promise<any> {
    console.log(`🤖 Executing AI Provider: ${node.data.config?.providerId}`);

    const config = node.data.config;
    const providerId = config?.providerId;
    const modelId = config?.modelId;
    const apiKey = config?.apiKey;

    if (!providerId || !modelId) {
      throw new Error('AI Provider node not properly configured');
    }

    // Check if tools are needed from classification
    const needsTools = input?.classification?.needsTools || input?.needsTools;
    const toolTypes = input?.classification?.toolTypes || input?.toolTypes || [];

    console.log(`🔧 Provider node - input:`, JSON.stringify(input, null, 2));
    console.log(`🔧 Provider node - needsTools: ${needsTools}, toolTypes:`, toolTypes);

    // Extract the actual user message from the input
    let userMessage = '';
    if (typeof input === 'string') {
      userMessage = input;
    } else if (typeof input === 'object') {
      // Try to extract the user message from various possible structures
      userMessage = input.userMessage || input.message || input.input || input.content;

      // If still no message found, check for indexed properties (from user input)
      if (!userMessage && typeof input['0'] === 'string' && typeof input['1'] === 'string') {
        userMessage = Object.keys(input).filter(k => !isNaN(Number(k))).map(k => input[k]).join('');
      }

      // Last resort: stringify the input (but this should be avoided)
      if (!userMessage) {
        console.warn('No clear user message found in AI provider input, using fallback');
        userMessage = JSON.stringify(input);
      }
    }

    console.log(`🔧 Provider extracted userMessage: "${userMessage}"`);
    console.log(`🔧 Provider full input object:`, JSON.stringify(input, null, 2));

    // Detect workflow capabilities
    const workflowCapabilities = allNodes ? this.detectWorkflowCapabilities(allNodes) : { tools: [], hasBrowsing: false, capabilities: '' };

    // Prepare conversation history for API call (like regular router configs)
    let conversationMessages: Array<{ role: string; content: string }> = [];

    // Add system message with capability context if capabilities exist
    if (workflowCapabilities.capabilities) {
      conversationMessages.push({
        role: 'system',
        content: workflowCapabilities.capabilities
      });
    }

    if (messages && messages.length > 0) {
      // Use full conversation history if available
      const historyMessages = messages.map(msg => ({
        role: msg.role,
        content: typeof msg.content === 'string' ? msg.content :
                 (Array.isArray(msg.content) && msg.content[0]?.text) ? msg.content[0].text :
                 JSON.stringify(msg.content)
      }));

      conversationMessages.push(...historyMessages);

      // Add current user message if it's not already the last message
      const lastMessage = conversationMessages[conversationMessages.length - 1];
      if (!lastMessage || lastMessage.content !== userMessage) {
        conversationMessages.push({ role: 'user', content: userMessage });
      }
    } else {
      // Fallback to single message if no conversation history
      conversationMessages.push({ role: 'user', content: userMessage });
    }

    console.log(`🤖 Processing conversation (${conversationMessages.length} messages) with ${providerId}/${modelId}`);

    let responseText = '';

    try {
      // If tools are needed, execute them first
      if (needsTools && toolTypes.length > 0 && allNodes && edges) {
        console.log(`🔧 Executing tools before AI response:`, toolTypes);
        responseText = await this.executeToolsAndGetResponse(
          toolTypes,
          userMessage,
          allNodes,
          edges,
          conversationMessages,
          providerId,
          modelId,
          apiKey,
          onStreamChunk,
          workflowId,
          userId
        );
      } else {
        // Get available functions for connected tools
        const connectedTools = this.getConnectedToolTypes(allNodes, edges);
        const availableFunctions = getFunctionsForTools(connectedTools);

        console.log(`🔧 Available functions for AI: ${availableFunctions.map(f => f.name).join(', ')}`);

        // Make AI API call with function calling support
        if (providerId === 'google' && apiKey) {
          responseText = await this.callGeminiAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
        } else if (providerId === 'openai' && apiKey) {
          responseText = await this.callOpenAIAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
        } else if (providerId === 'anthropic' && apiKey) {
          responseText = await this.callAnthropicAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
        } else if (providerId === 'deepseek' && apiKey) {
          responseText = await this.callDeepSeekAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
        } else if (providerId === 'xai' && apiKey) {
          responseText = await this.callXAIAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
        } else if (providerId === 'openrouter' && apiKey) {
          responseText = await this.callOpenRouterAPI(conversationMessages, modelId, apiKey, onStreamChunk, availableFunctions, userId);
        } else {
          // Fallback for providers without API implementation or missing API key
          responseText = `Hello! I'm a ${providerId} AI assistant using ${modelId}. I received your message: "${userMessage}". This is a placeholder response as the API integration is not yet implemented for this provider.`;

          // If streaming is enabled, simulate streaming for fallback response
          if (onStreamChunk) {
            const words = responseText.split(' ');
            for (let i = 0; i < words.length; i++) {
              const chunk = (i === 0 ? '' : ' ') + words[i];
              onStreamChunk(chunk);
              // Small delay to simulate streaming
              await new Promise(resolve => setTimeout(resolve, 50));
            }
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error calling ${providerId} API:`, error);
      responseText = `I apologize, but I encountered an error while processing your request. Please try again later.`;

      // If streaming is enabled, stream the error message
      if (onStreamChunk) {
        const words = responseText.split(' ');
        for (let i = 0; i < words.length; i++) {
          const chunk = (i === 0 ? '' : ' ') + words[i];
          onStreamChunk(chunk);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    }

    // Return structured response
    const response = {
      provider: providerId,
      model: modelId,
      response: responseText,
      metadata: {
        inputType: typeof input,
        hasApiKey: !!apiKey,
        timestamp: new Date().toISOString(),
        tokenCount: responseText.length / 4 // Rough estimate
      }
    };

    console.log(`✅ AI Provider response generated: ${responseText.length} characters`);
    return response;
  }

  /**
   * Get connected tool types from workflow nodes
   */
  private getConnectedToolTypes(allNodes?: WorkflowNode[], edges?: any[]): string[] {
    if (!allNodes || !edges) return [];

    const toolNodes = allNodes.filter(node => node.type === 'tool');
    const connectedTools: string[] = [];

    for (const toolNode of toolNodes) {
      // Check if tool node is connected and authenticated
      const isConnected = edges.some(edge =>
        edge.source === toolNode.id || edge.target === toolNode.id
      );

      if (isConnected && toolNode.data?.config?.toolType &&
          toolNode.data?.config?.isAuthenticated) {
        connectedTools.push(toolNode.data.config.toolType);
      }
    }

    return connectedTools;
  }

  /**
   * Call Gemini API using OpenAI-compatible endpoint with function calling support
   */
  private async callGeminiAPI(
    messages: Array<{ role: string; content: string }>,
    model: string,
    apiKey: string,
    onStreamChunk?: (chunk: string) => void,
    availableFunctions?: FunctionSchema[],
    userId?: string
  ): Promise<string> {
    // Extract just the model name without provider prefix
    const modelName = model.includes('/') ? model.split('/').pop() : model;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Connection': 'keep-alive',
        'User-Agent': 'RoKey/1.0 (Workflow)',
        'Origin': 'https://rokey.app',
      },
      body: JSON.stringify({
        model: modelName,
        messages: messages, // Use full conversation history
        temperature: 0.7,
        max_tokens: 1000,
        stream: !!onStreamChunk, // Enable streaming if callback provided
        ...(availableFunctions && availableFunctions.length > 0 && {
          tools: availableFunctions.map(func => ({
            type: 'function',
            function: func
          })),
          tool_choice: 'auto'
        })
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    if (onStreamChunk && response.body) {
      // Handle streaming response with function calling support
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let accumulatedResponse = '';
      let buffer = ''; // Buffer for incomplete lines
      let functionCalls: any[] = [];
      let hasToolCalls = false;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Process complete lines
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();
              if (data === '[DONE]') continue;
              if (!data) continue; // Skip empty data

              try {
                const parsed = JSON.parse(data);
                const delta = parsed.choices?.[0]?.delta;

                // Handle regular content
                const content = delta?.content || '';
                if (content) {
                  accumulatedResponse += content;
                  onStreamChunk(content);
                }

                // Handle function calls in streaming
                if (delta?.tool_calls) {
                  hasToolCalls = true;
                  for (const toolCall of delta.tool_calls) {
                    if (toolCall.type === 'function') {
                      // Find or create function call entry
                      let existingCall = functionCalls.find(fc => fc.index === toolCall.index);
                      if (!existingCall) {
                        existingCall = {
                          index: toolCall.index,
                          name: toolCall.function?.name || '',
                          arguments: toolCall.function?.arguments || ''
                        };
                        functionCalls.push(existingCall);
                      } else {
                        // Accumulate function arguments
                        if (toolCall.function?.arguments) {
                          existingCall.arguments += toolCall.function.arguments;
                        }
                      }
                    }
                  }
                }
              } catch (parseError) {
                console.warn(`[Gemini Workflow] Error parsing streaming data: ${parseError}. Data: ${data}`);
              }
            }
          }
        }

        // Process any remaining data in buffer
        if (buffer.trim()) {
          const line = buffer.trim();
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (data && data !== '[DONE]') {
              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content || '';
                if (content) {
                  accumulatedResponse += content;
                  onStreamChunk(content);
                }
              } catch (parseError) {
                console.warn(`[Gemini Workflow] Error parsing final streaming data: ${parseError}. Data: ${data}`);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      console.log(`[Gemini Workflow] Streaming completed. Total response: ${accumulatedResponse.length} characters`);

      // Handle function calls if present
      if (hasToolCalls && functionCalls.length > 0 && userId && availableFunctions) {
        console.log(`[Gemini Workflow] Processing ${functionCalls.length} function calls from streaming`);

        const functionHandler = new FunctionCallHandler(userId);
        const functionResults = await functionHandler.executeFunctionCalls(
          functionCalls.map(fc => ({ name: fc.name, arguments: fc.arguments }))
        );

        // Add function results to conversation and make another API call
        const functionResultsMessage = formatFunctionCallResults(functionResults);
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: accumulatedResponse },
          { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
        ];

        // Make another call without streaming to get the final response
        return await this.callGeminiAPI(updatedMessages, model, apiKey, onStreamChunk);
      }

      return accumulatedResponse || 'No response generated';
    } else {
      // Handle non-streaming response
      const data = await response.json();

      // Check for function calls
      if (hasFunctionCalls(data) && userId && availableFunctions) {
        const functionCalls = extractFunctionCalls(data);
        const functionHandler = new FunctionCallHandler(userId);
        const functionResults = await functionHandler.executeFunctionCalls(functionCalls);

        // Add function results to conversation and make another API call
        const functionResultsMessage = formatFunctionCallResults(functionResults);
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: data.choices?.[0]?.message?.content || '' },
          { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
        ];

        // Make another call without functions to get the final response
        return await this.callGeminiAPI(updatedMessages, model, apiKey, onStreamChunk);
      }

      return data.choices?.[0]?.message?.content || 'No response generated';
    }
  }

  /**
   * Call OpenAI API with function calling support
   */
  private async callOpenAIAPI(
    messages: Array<{ role: string; content: string }>,
    model: string,
    apiKey: string,
    onStreamChunk?: (chunk: string) => void,
    availableFunctions?: FunctionSchema[],
    userId?: string
  ): Promise<string> {
    // Extract just the model name without provider prefix
    const modelName = model.includes('/') ? model.split('/').pop() : model;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Origin': 'https://rokey.app',
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=30, max=100',
        'Cache-Control': 'no-cache',
        'Priority': 'u=1, i',
      },
      body: JSON.stringify({
        model: modelName,
        messages: messages, // Use full conversation history
        temperature: 0.7,
        max_tokens: 1000,
        stream: !!onStreamChunk, // Enable streaming if callback provided
        ...(availableFunctions && availableFunctions.length > 0 && {
          tools: availableFunctions.map(func => ({
            type: 'function',
            function: func
          })),
          tool_choice: 'auto'
        })
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    if (onStreamChunk && response.body) {
      // Use helper function for OpenAI-compatible streaming with function calling
      return await this.handleOpenAICompatibleStreaming(
        response,
        messages,
        model,
        apiKey,
        onStreamChunk,
        availableFunctions,
        userId,
        'OpenAI',
        this.callOpenAIAPI.bind(this)
      );
    } else {
      // Handle non-streaming response
      const data = await response.json();

      // Check for function calls
      if (hasFunctionCalls(data) && userId && availableFunctions) {
        const functionCalls = extractFunctionCalls(data);
        const functionHandler = new FunctionCallHandler(userId);
        const functionResults = await functionHandler.executeFunctionCalls(functionCalls);

        // Add function results to conversation and make another API call
        const functionResultsMessage = formatFunctionCallResults(functionResults);
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: data.choices?.[0]?.message?.content || '' },
          { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
        ];

        // Make another call without functions to get the final response
        return await this.callOpenAIAPI(updatedMessages, model, apiKey, onStreamChunk);
      }

      return data.choices?.[0]?.message?.content || 'No response generated';
    }
  }

  /**
   * Call Anthropic API with function calling support
   */
  private async callAnthropicAPI(
    messages: Array<{ role: string; content: string }>,
    model: string,
    apiKey: string,
    onStreamChunk?: (chunk: string) => void,
    availableFunctions?: FunctionSchema[],
    userId?: string
  ): Promise<string> {
    // Extract just the model name without provider prefix
    const modelName = model.includes('/') ? model.split('/').pop() : model;

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'Origin': 'https://rokey.app',
        'User-Agent': 'RoKey/1.0 (Workflow)',
      },
      body: JSON.stringify({
        model: modelName,
        max_tokens: 1000,
        messages: messages, // Use full conversation history
        stream: !!onStreamChunk, // Enable streaming if callback provided
        ...(availableFunctions && availableFunctions.length > 0 && {
          tools: availableFunctions.map(func => ({
            name: func.name,
            description: func.description,
            input_schema: func.parameters
          }))
        })
      })
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
    }

    if (onStreamChunk && response.body) {
      // Handle streaming response with function calling support
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let accumulatedResponse = '';
      let buffer = ''; // Buffer for incomplete lines
      let functionCalls: any[] = [];
      let hasToolCalls = false;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Process complete lines
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();
              if (data === '[DONE]') continue;
              if (!data) continue; // Skip empty data

              try {
                const parsed = JSON.parse(data);

                // Handle regular content (Anthropic format)
                const content = parsed.delta?.text || '';
                if (content) {
                  accumulatedResponse += content;
                  onStreamChunk(content);
                }

                // Handle function calls in streaming (Anthropic format)
                if (parsed.delta?.tool_use) {
                  hasToolCalls = true;
                  const toolUse = parsed.delta.tool_use;
                  functionCalls.push({
                    name: toolUse.name,
                    arguments: JSON.stringify(toolUse.input || {})
                  });
                }
              } catch (parseError) {
                console.warn(`[Anthropic Workflow] Error parsing streaming data: ${parseError}. Data: ${data}`);
              }
            }
          }
        }

        // Process any remaining data in buffer
        if (buffer.trim()) {
          const line = buffer.trim();
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (data && data !== '[DONE]') {
              try {
                const parsed = JSON.parse(data);
                const content = parsed.delta?.text || '';
                if (content) {
                  accumulatedResponse += content;
                  onStreamChunk(content);
                }
              } catch (parseError) {
                console.warn(`[Anthropic Workflow] Error parsing final streaming data: ${parseError}. Data: ${data}`);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      console.log(`[Anthropic Workflow] Streaming completed. Total response: ${accumulatedResponse.length} characters`);

      // Handle function calls if present
      if (hasToolCalls && functionCalls.length > 0 && userId && availableFunctions) {
        console.log(`[Anthropic Workflow] Processing ${functionCalls.length} function calls from streaming`);

        const functionHandler = new FunctionCallHandler(userId);
        const functionResults = await functionHandler.executeFunctionCalls(functionCalls);

        // Add function results to conversation and make another API call
        const functionResultsMessage = formatFunctionCallResults(functionResults);
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: accumulatedResponse },
          { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
        ];

        // Make another call without streaming to get the final response
        return await this.callAnthropicAPI(updatedMessages, model, apiKey, onStreamChunk);
      }

      return accumulatedResponse || 'No response generated';
    } else {
      // Handle non-streaming response
      const data = await response.json();

      // Check for tool use (Anthropic format)
      if (data.content && userId && availableFunctions) {
        const toolUseBlocks = data.content.filter((block: any) => block.type === 'tool_use');

        if (toolUseBlocks.length > 0) {
          const functionCalls: FunctionCall[] = toolUseBlocks.map((block: any) => ({
            name: block.name,
            arguments: block.input
          }));

          const functionHandler = new FunctionCallHandler(userId);
          const functionResults = await functionHandler.executeFunctionCalls(functionCalls);

          // Add function results to conversation and make another API call
          const functionResultsMessage = formatFunctionCallResults(functionResults);
          const updatedMessages = [
            ...messages,
            { role: 'assistant', content: data.content?.[0]?.text || '' },
            { role: 'user', content: `Function call results:\n${functionResultsMessage}` }
          ];

          // Make another call without functions to get the final response
          return await this.callAnthropicAPI(updatedMessages, model, apiKey, onStreamChunk);
        }
      }

      return data.content?.[0]?.text || 'No response generated';
    }
  }

  /**
   * Call DeepSeek API using OpenAI-compatible endpoint with function calling support
   */
  private async callDeepSeekAPI(
    messages: Array<{ role: string; content: string }>,
    model: string,
    apiKey: string,
    onStreamChunk?: (chunk: string) => void,
    availableFunctions?: FunctionSchema[],
    userId?: string
  ): Promise<string> {
    // Extract just the model name without provider prefix
    const modelName = model.includes('/') ? model.split('/').pop() : model;

    const response = await fetch('https://api.deepseek.com/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Origin': 'https://rokey.app',
        'User-Agent': 'RoKey/1.0 (Workflow)',
      },
      body: JSON.stringify({
        model: modelName,
        messages: messages, // Use full conversation history
        temperature: 0.7,
        max_tokens: 1000,
        stream: !!onStreamChunk, // Enable streaming if callback provided
        ...(availableFunctions && availableFunctions.length > 0 && {
          tools: availableFunctions.map(func => ({
            type: 'function',
            function: func
          })),
          tool_choice: 'auto'
        })
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
    }

    if (onStreamChunk && response.body) {
      // Use helper function for OpenAI-compatible streaming with function calling
      return await this.handleOpenAICompatibleStreaming(
        response,
        messages,
        model,
        apiKey,
        onStreamChunk,
        availableFunctions,
        userId,
        'DeepSeek',
        this.callDeepSeekAPI.bind(this)
      );
    } else {
      // Handle non-streaming response
      const data = await response.json();

      // Check for function calls (OpenAI-compatible format)
      if (hasFunctionCalls(data) && userId && availableFunctions) {
        const functionCalls = extractFunctionCalls(data);
        const functionHandler = new FunctionCallHandler(userId);
        const functionResults = await functionHandler.executeFunctionCalls(functionCalls);

        // Add function results to conversation and make another API call
        const functionResultsMessage = formatFunctionCallResults(functionResults);
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: data.choices?.[0]?.message?.content || '' },
          { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
        ];

        // Make another call without functions to get the final response
        return await this.callDeepSeekAPI(updatedMessages, model, apiKey, onStreamChunk);
      }

      return data.choices?.[0]?.message?.content || 'No response generated';
    }
  }

  /**
   * Call XAI (Grok) API using OpenAI-compatible endpoint with function calling support
   */
  private async callXAIAPI(
    messages: Array<{ role: string; content: string }>,
    model: string,
    apiKey: string,
    onStreamChunk?: (chunk: string) => void,
    availableFunctions?: FunctionSchema[],
    userId?: string
  ): Promise<string> {
    // Extract just the model name without provider prefix
    const modelName = model.includes('/') ? model.split('/').pop() : model;

    const response = await fetch('https://api.x.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Origin': 'https://rokey.app',
        'User-Agent': 'RoKey/1.0 (Workflow)',
      },
      body: JSON.stringify({
        model: modelName,
        messages: messages, // Use full conversation history
        temperature: 0.7,
        max_tokens: 1000,
        stream: !!onStreamChunk, // Enable streaming if callback provided
        ...(availableFunctions && availableFunctions.length > 0 && {
          tools: availableFunctions.map(func => ({
            type: 'function',
            function: func
          })),
          tool_choice: 'auto'
        })
      })
    });

    if (!response.ok) {
      throw new Error(`XAI API error: ${response.status} ${response.statusText}`);
    }

    if (onStreamChunk && response.body) {
      // Use helper function for OpenAI-compatible streaming with function calling
      return await this.handleOpenAICompatibleStreaming(
        response,
        messages,
        model,
        apiKey,
        onStreamChunk,
        availableFunctions,
        userId,
        'XAI',
        this.callXAIAPI.bind(this)
      );
    } else {
      // Handle non-streaming response
      const data = await response.json();

      // Check for function calls (OpenAI-compatible format)
      if (hasFunctionCalls(data) && userId && availableFunctions) {
        const functionCalls = extractFunctionCalls(data);
        const functionHandler = new FunctionCallHandler(userId);
        const functionResults = await functionHandler.executeFunctionCalls(functionCalls);

        // Add function results to conversation and make another API call
        const functionResultsMessage = formatFunctionCallResults(functionResults);
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: data.choices?.[0]?.message?.content || '' },
          { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
        ];

        // Make another call without functions to get the final response
        return await this.callXAIAPI(updatedMessages, model, apiKey, onStreamChunk);
      }

      return data.choices?.[0]?.message?.content || 'No response generated';
    }
  }

  /**
   * Call OpenRouter API using OpenAI-compatible endpoint with function calling support
   */
  private async callOpenRouterAPI(
    messages: Array<{ role: string; content: string }>,
    model: string,
    apiKey: string,
    onStreamChunk?: (chunk: string) => void,
    availableFunctions?: FunctionSchema[],
    userId?: string
  ): Promise<string> {
    // For OpenRouter, keep the full model path as it expects provider/model format
    const modelName = model;

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Origin': 'https://rokey.app',
        'User-Agent': 'RoKey/1.0 (Workflow)',
        'HTTP-Referer': 'https://rokey.app',
        'X-Title': 'RouKey Workflow',
      },
      body: JSON.stringify({
        model: modelName,
        messages: messages, // Use full conversation history
        temperature: 0.7,
        max_tokens: 1000,
        stream: !!onStreamChunk, // Enable streaming if callback provided
        ...(availableFunctions && availableFunctions.length > 0 && {
          tools: availableFunctions.map(func => ({
            type: 'function',
            function: func
          })),
          tool_choice: 'auto'
        })
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    if (onStreamChunk && response.body) {
      // Use helper function for OpenAI-compatible streaming with function calling
      return await this.handleOpenAICompatibleStreaming(
        response,
        messages,
        model,
        apiKey,
        onStreamChunk,
        availableFunctions,
        userId,
        'OpenRouter',
        this.callOpenRouterAPI.bind(this)
      );
    } else {
      // Handle non-streaming response
      const data = await response.json();

      // Check for function calls (OpenAI-compatible format)
      if (hasFunctionCalls(data) && userId && availableFunctions) {
        const functionCalls = extractFunctionCalls(data);
        const functionHandler = new FunctionCallHandler(userId);
        const functionResults = await functionHandler.executeFunctionCalls(functionCalls);

        // Add function results to conversation and make another API call
        const functionResultsMessage = formatFunctionCallResults(functionResults);
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: data.choices?.[0]?.message?.content || '' },
          { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
        ];

        // Make another call without functions to get the final response
        return await this.callOpenRouterAPI(updatedMessages, model, apiKey, onStreamChunk);
      }

      return data.choices?.[0]?.message?.content || 'No response generated';
    }
  }

  /**
   * Generic method to call any provider API
   */
  private async callProviderAPI(providerId: string, message: string, modelId: string, apiKey: string, onStreamChunk?: (chunk: string) => void): Promise<string> {
    // Convert string message to messages array format
    const messages = [{ role: 'user', content: message }];

    switch (providerId) {
      case 'google':
        return await this.callGeminiAPI(messages, modelId, apiKey, onStreamChunk);
      case 'openai':
        return await this.callOpenAIAPI(messages, modelId, apiKey, onStreamChunk);
      case 'anthropic':
        return await this.callAnthropicAPI(messages, modelId, apiKey, onStreamChunk);
      case 'deepseek':
        return await this.callDeepSeekAPI(messages, modelId, apiKey, onStreamChunk);
      case 'xai':
        return await this.callXAIAPI(messages, modelId, apiKey, onStreamChunk);
      case 'openrouter':
        return await this.callOpenRouterAPI(messages, modelId, apiKey, onStreamChunk);
      default:
        throw new Error(`Unsupported provider: ${providerId}`);
    }
  }

  /**
   * Execute a Planner node - Uses LLM to create intelligent browsing plans
   */
  private async executePlannerNode(node: WorkflowNode, input: any): Promise<any> {
    console.log(`📋 Executing Planner: ${node.data.config?.modelId}`);

    const config = node.data.config as any;
    const task = typeof input === 'string' ? input : 'Plan browsing task';
    const maxSubtasks = config?.maxSubtasks || 5;

    // If planner has AI provider configuration, use LLM for intelligent planning
    if (config?.providerId && config?.modelId) {
      try {
        const apiKey = await this.getApiKeyForProvider(config.providerId);
        if (apiKey) {
          const planningPrompt = `Create a detailed browsing plan for this task: "${task}"

Generate a JSON plan with ${maxSubtasks} subtasks. Each subtask should have:
- id: unique identifier
- type: 'search', 'navigate', 'analyze_results', or 'check_completion'
- description: what this step accomplishes
- target: what to search for or navigate to
- parameters: { extractionGoal: specific goal for this step }

Focus on efficient information gathering and logical progression.

Respond with JSON only:
{
  "subtasks": [
    {
      "id": "step_1",
      "type": "search",
      "description": "...",
      "target": "...",
      "parameters": { "extractionGoal": "..." }
    }
  ]
}`;

          // Planner runs silently - don't stream planning output to user
          const planResponse = await this.callProviderAPI(config.providerId, planningPrompt, config.modelId, apiKey);

          try {
            // Parse LLM response to extract plan
            const cleanResponse = planResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            const aiPlan = JSON.parse(cleanResponse);

            const plan = {
              id: `planner_${Date.now()}`,
              task,
              subtasks: aiPlan.subtasks.map((subtask: any, index: number) => ({
                ...subtask,
                status: 'pending',
                order: index + 1
              })),
              estimatedTime: Math.min(aiPlan.subtasks.length * 2, 10),
              priority: 'medium',
              timestamp: new Date().toISOString(),
              generatedBy: `${config.providerId}:${config.modelId}`
            };

            console.log(`📋 AI-generated plan with ${plan.subtasks.length} subtasks`);
            return plan;
          } catch (parseError) {
            console.warn('Failed to parse AI plan, falling back to default plan');
          }
        }
      } catch (error) {
        console.warn('Failed to generate AI plan, falling back to default plan:', error);
      }
    }

    // Fallback to static plan if no AI provider or AI call fails
    const plan = {
      id: `planner_${Date.now()}`,
      task,
      subtasks: [
        {
          id: 'search_primary',
          type: 'search',
          description: `Primary search for: ${task}`,
          target: task,
          status: 'pending',
          parameters: {
            extractionGoal: 'Find relevant websites and initial information'
          }
        },
        {
          id: 'analyze_results',
          type: 'analyze_results',
          description: 'Analyze search results and select best websites',
          target: 'search_results',
          status: 'pending',
          parameters: {
            extractionGoal: 'Select top websites based on relevance and authority'
          }
        }
      ].slice(0, maxSubtasks),
      estimatedTime: Math.min(maxSubtasks * 2, 10),
      priority: 'medium',
      timestamp: new Date().toISOString(),
      generatedBy: 'static_fallback'
    };

    console.log(`📋 Created fallback plan with ${plan.subtasks.length} subtasks`);
    return plan;
  }

  /**
   * Get execution status
   */
  getExecutionStatus(workflowId: string): WorkflowExecution | null {
    return this.activeExecutions.get(workflowId) || null;
  }

  /**
   * Get all active executions
   */
  getActiveExecutions(): WorkflowExecution[] {
    return Array.from(this.activeExecutions.values());
  }

  /**
   * Execute Classifier Node - Analyzes user input to determine roles, browsing needs, and tool requirements
   */
  private async executeClassifierNode(
    node: WorkflowNode,
    input: any,
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[],
    onStreamChunk?: (chunk: string) => void,
    messages?: Array<{ role: string; content: any }>
  ): Promise<any> {
    console.log(`🔍 Executing Classifier Node: ${node.id}`);

    const config = node.data.config as any;
    const userPrompt = typeof input === 'string' ? input : input?.content || input?.prompt || '';

    if (!userPrompt.trim()) {
      throw new Error('No user input provided for classification');
    }

    // Get classification API key
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      throw new Error('Classification service unavailable - no API key');
    }

    // Find connected router to get available roles
    const routerConnections = edges.filter(edge =>
      edge.source === node.id &&
      allNodes.find(n => n.id === edge.target)?.type === 'centralRouter'
    );

    if (routerConnections.length === 0) {
      throw new Error('Classifier must be connected to a Central Router');
    }

    const routerNode = allNodes.find(n => n.id === routerConnections[0].target);
    const availableRoles = this.getAvailableRolesFromRouter(routerNode!, allNodes, edges);

    // Get available tools from the workflow
    const availableTools = this.getAvailableToolsFromWorkflow(allNodes, edges);

    // Debug: Log available tools for classification
    console.log(`🔧 Available tools for classification:`, availableTools);

    // Prepare conversation history for classification context
    let conversationHistory: Array<{ role: string; content: string }> = [];
    if (messages && messages.length > 0) {
      conversationHistory = messages.map(msg => ({
        role: msg.role,
        content: typeof msg.content === 'string' ? msg.content :
                 (Array.isArray(msg.content) && msg.content[0]?.text) ? msg.content[0].text :
                 JSON.stringify(msg.content)
      }));
    }

    // Perform comprehensive classification (silently - don't stream to user)
    const classification = await this.performComprehensiveClassification(
      userPrompt,
      availableRoles,
      classificationApiKey,
      availableTools,
      undefined, // Don't pass onStreamChunk - classifier should run silently
      conversationHistory
    );

    console.log(`✅ Classification complete:`, classification);

    return {
      ...input,
      classification,
      classifiedRoles: classification.roles,
      needsBrowsing: classification.needsBrowsing,
      needsTools: classification.needsTools,
      toolTypes: classification.toolTypes,
      isMultiRole: classification.isMultiRole,
      reasoning: classification.reasoning
    };
  }

  /**
   * Execute Tool Node - Handles external tool integrations
   */
  private async executeToolNode(
    node: WorkflowNode,
    input: any,
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[]
  ): Promise<any> {
    console.log(`🔧 Executing Tool Node: ${node.id}`);

    const config = node.data.config as any;

    if (!config.toolType) {
      throw new Error('Tool type not configured');
    }

    // For now, only web browsing is implemented
    if (config.toolType === 'web_browsing') {
      // Web browsing is handled by the browsing node, not tool node
      console.log(`🌐 Web browsing detected - delegating to browsing system`);
      return {
        ...input,
        toolResult: {
          type: 'web_browsing',
          status: 'delegated_to_browsing_node',
          message: 'Web browsing handled by dedicated browsing node'
        }
      };
    }

    // For other tools, return not implemented for now
    return {
      ...input,
      toolResult: {
        type: config.toolType,
        status: 'not_implemented',
        message: `${config.toolType} integration coming soon`
      }
    };
  }

  /**
   * Execute Output Node - Handles final response streaming and formatting
   */
  private async executeOutputNode(
    node: WorkflowNode,
    input: any,
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[]
  ): Promise<any> {
    console.log(`📤 Executing Output Node: ${node.id}`);

    const config = node.data.config as any;
    const outputFormat = config.outputFormat || 'stream';
    const chunkSize = config.chunkSize || 15;

    // Extract the final response from input
    let finalResponse: string;

    console.log(`📤 Output Node received input:`, JSON.stringify(input, null, 2));

    if (typeof input === 'string') {
      finalResponse = input;
    } else if (input?.response) {
      // Response from AI provider node
      finalResponse = input.response;
      console.log(`📤 Extracted response from input.response: ${finalResponse.substring(0, 100)}...`);
    } else if (input?.content) {
      finalResponse = input.content;
      console.log(`📤 Extracted response from input.content: ${finalResponse.substring(0, 100)}...`);
    } else if (input?.result) {
      finalResponse = input.result;
      console.log(`📤 Extracted response from input.result: ${finalResponse.substring(0, 100)}...`);
    } else if (input?.finalOutput) {
      finalResponse = input.finalOutput;
      console.log(`📤 Extracted response from input.finalOutput: ${finalResponse.substring(0, 100)}...`);
    } else {
      // Try to extract from nested structures
      try {
        // Check if it's a JSON string that needs parsing
        if (typeof input === 'object' && input !== null) {
          // Look for response in nested objects
          const responseText = this.extractResponseFromNestedObject(input);
          if (responseText) {
            finalResponse = responseText;
            console.log(`📤 Extracted response from nested object: ${finalResponse.substring(0, 100)}...`);
          } else {
            console.warn(`📤 No response found in input, falling back to JSON stringify`);
            finalResponse = JSON.stringify(input);
          }
        } else {
          finalResponse = 'Workflow completed successfully.';
        }
      } catch (error) {
        console.error('Error extracting response:', error);
        finalResponse = 'Workflow completed successfully.';
      }
    }

    console.log(`📤 Output Node streaming response (${finalResponse.length} chars)`);

    // For workflow execution, we'll store the final result
    // The actual streaming will be handled by the workflow API endpoint
    return {
      ...input,
      finalOutput: finalResponse,
      outputConfig: {
        format: outputFormat,
        chunkSize,
        timestamp: new Date().toISOString()
      },
      isComplete: true
    };
  }

  /**
   * Helper function for OpenAI-compatible streaming with function calling support
   */
  private async handleOpenAICompatibleStreaming(
    response: Response,
    messages: Array<{ role: string; content: string }>,
    model: string,
    apiKey: string,
    onStreamChunk: (chunk: string) => void,
    availableFunctions?: FunctionSchema[],
    userId?: string,
    providerName: string = 'OpenAI',
    apiCallMethod: (messages: any[], model: string, apiKey: string, onStreamChunk?: (chunk: string) => void) => Promise<string>
  ): Promise<string> {
    const reader = response.body!.getReader();
    const decoder = new TextDecoder();
    let accumulatedResponse = '';
    let buffer = ''; // Buffer for incomplete lines
    let functionCalls: any[] = [];
    let hasToolCalls = false;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (data === '[DONE]') continue;
            if (!data) continue; // Skip empty data

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices?.[0]?.delta;

              // Handle regular content
              const content = delta?.content || '';
              if (content) {
                accumulatedResponse += content;
                onStreamChunk(content);
              }

              // Handle function calls in streaming
              if (delta?.tool_calls) {
                hasToolCalls = true;
                for (const toolCall of delta.tool_calls) {
                  if (toolCall.type === 'function') {
                    // Find or create function call entry
                    let existingCall = functionCalls.find(fc => fc.index === toolCall.index);
                    if (!existingCall) {
                      existingCall = {
                        index: toolCall.index,
                        name: toolCall.function?.name || '',
                        arguments: toolCall.function?.arguments || ''
                      };
                      functionCalls.push(existingCall);
                    } else {
                      // Accumulate function arguments
                      if (toolCall.function?.arguments) {
                        existingCall.arguments += toolCall.function.arguments;
                      }
                    }
                  }
                }
              }
            } catch (parseError) {
              console.warn(`[${providerName} Workflow] Error parsing streaming data: ${parseError}. Data: ${data}`);
            }
          }
        }
      }

      // Process any remaining data in buffer
      if (buffer.trim()) {
        const line = buffer.trim();
        if (line.startsWith('data: ')) {
          const data = line.slice(6).trim();
          if (data && data !== '[DONE]') {
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content || '';
              if (content) {
                accumulatedResponse += content;
                onStreamChunk(content);
              }
            } catch (parseError) {
              console.warn(`[${providerName} Workflow] Error parsing final streaming data: ${parseError}. Data: ${data}`);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    console.log(`[${providerName} Workflow] Streaming completed. Total response: ${accumulatedResponse.length} characters`);

    // Handle function calls if present
    if (hasToolCalls && functionCalls.length > 0 && userId && availableFunctions) {
      console.log(`[${providerName} Workflow] Processing ${functionCalls.length} function calls from streaming`);

      const functionHandler = new FunctionCallHandler(userId);
      const functionResults = await functionHandler.executeFunctionCalls(
        functionCalls.map(fc => ({ name: fc.name, arguments: fc.arguments }))
      );

      // Add function results to conversation and make another API call
      const functionResultsMessage = formatFunctionCallResults(functionResults);
      const updatedMessages = [
        ...messages,
        { role: 'assistant', content: accumulatedResponse },
        { role: 'system', content: `Function call results:\n${functionResultsMessage}` }
      ];

      // Make another call without streaming to get the final response
      return await apiCallMethod(updatedMessages, model, apiKey, onStreamChunk);
    }

    return accumulatedResponse || 'No response generated';
  }

  /**
   * Fix common JSON formatting issues
   */
  private fixCommonJsonIssues(jsonString: string): string {
    let cleaned = jsonString;

    // Fix trailing commas
    cleaned = cleaned.replace(/,(\s*[}\]])/g, '$1');

    // Fix missing quotes around property names (but be careful not to break existing quoted ones)
    cleaned = cleaned.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');

    // Remove any trailing commas before closing braces/brackets
    cleaned = cleaned.replace(/,\s*}/g, '}');
    cleaned = cleaned.replace(/,\s*]/g, ']');

    // Fix any remaining control characters that might cause issues
    cleaned = cleaned.replace(/[\x00-\x1F\x7F]/g, '');

    // Fix unescaped quotes within string values - this is the main issue
    // Look for patterns like: "key": "value with "quotes" inside"
    cleaned = cleaned.replace(/"([^"]*)"(\s*:\s*)"([^"]*)"([^"]*)"([^"]*)"([^",}]*)/g, '"$1"$2"$3\\"$4\\"$5"');

    // More aggressive fix for nested quotes in values
    // Find string values that contain unescaped quotes and escape them
    cleaned = cleaned.replace(/:\s*"([^"]*)"([^",}]*)"([^",}]*)/g, (match, p1, p2, p3) => {
      // If p2 contains characters that suggest it's part of the value, escape the quotes
      if (p2.trim() && !p2.includes(',') && !p2.includes('}')) {
        return `: "${p1}\\"${p2}\\"${p3}"`;
      }
      return match;
    });

    return cleaned;
  }

  /**
   * Robust JSON parsing with multiple fallback strategies
   */
  private parseJsonRobustly(content: string): any {
    console.log(`🔍 Raw content to parse (first 300 chars):`, content.substring(0, 300));

    // Strategy 1: Basic cleaning and parsing
    try {
      let cleanContent = content
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim();

      // Extract JSON object more carefully
      const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanContent = jsonMatch[0];
      }

      cleanContent = this.fixCommonJsonIssues(cleanContent);
      console.log(`🔍 Strategy 1 cleaned content:`, cleanContent.substring(0, 200));
      return JSON.parse(cleanContent);
    } catch (error1) {
      console.warn('Strategy 1 failed:', error1);
    }

    // Strategy 2: More aggressive cleaning
    try {
      let cleanContent = content
        .replace(/```json\n?/gi, '')
        .replace(/```\n?/gi, '')
        .replace(/\\n/g, '\n')
        .replace(/\\t/g, '\t')
        .replace(/\\r/g, '\r')
        .trim();

      // Find the JSON object boundaries more carefully
      const startIndex = cleanContent.indexOf('{');
      const endIndex = cleanContent.lastIndexOf('}');

      if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
        cleanContent = cleanContent.substring(startIndex, endIndex + 1);
      }

      cleanContent = this.fixCommonJsonIssues(cleanContent);
      console.log(`🔍 Strategy 2 cleaned content:`, cleanContent.substring(0, 200));
      return JSON.parse(cleanContent);
    } catch (error2) {
      console.warn('Strategy 2 failed:', error2);
    }

    // Strategy 3: Character-by-character cleaning
    try {
      let cleanContent = content
        .replace(/```json\n?/gi, '')
        .replace(/```\n?/gi, '')
        .trim();

      // Extract JSON more aggressively
      const startIndex = cleanContent.indexOf('{');
      if (startIndex !== -1) {
        cleanContent = cleanContent.substring(startIndex);

        // Find matching closing brace
        let braceCount = 0;
        let endIndex = -1;
        for (let i = 0; i < cleanContent.length; i++) {
          if (cleanContent[i] === '{') braceCount++;
          if (cleanContent[i] === '}') {
            braceCount--;
            if (braceCount === 0) {
              endIndex = i;
              break;
            }
          }
        }

        if (endIndex !== -1) {
          cleanContent = cleanContent.substring(0, endIndex + 1);
        }
      }

      // More aggressive JSON fixing
      cleanContent = cleanContent
        // Fix escaped quotes
        .replace(/\\"/g, '"')
        // Fix unescaped quotes in values
        .replace(/:\s*"([^"]*)"([^",}]*)"([^",}]*)"([^",}]*)/g, ': "$1\\"$2\\"$3"')
        // Fix trailing commas
        .replace(/,(\s*[}\]])/g, '$1')
        // Fix missing commas between properties
        .replace(/"\s*"([a-zA-Z_])/g, '", "$1')
        // Fix property names without quotes
        .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
        // Remove control characters
        .replace(/[\x00-\x1F\x7F]/g, '');

      console.log(`🔍 Strategy 3 cleaned content:`, cleanContent.substring(0, 200));
      return JSON.parse(cleanContent);
    } catch (error3) {
      console.warn('Strategy 3 failed:', error3);
    }

    // Strategy 4: Targeted fix for quote issues in string values
    try {
      let cleanContent = content
        .replace(/```json\n?/gi, '')
        .replace(/```\n?/gi, '')
        .trim();

      // Extract JSON object
      const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanContent = jsonMatch[0];
      }

      // Specifically target the quote issue in reason field and similar string values
      // Pattern: "key": "value with "unescaped quotes" in middle"
      cleanContent = cleanContent.replace(
        /("reason"\s*:\s*"[^"]*)"([^"]*)"([^"]*)"([^"]*)/g,
        '$1\\"$2\\"$3'
      );

      // More general pattern for any string value with unescaped quotes
      cleanContent = cleanContent.replace(
        /("[\w_]+"\s*:\s*"[^"]*)"([^",}]*)"([^",}]*)/g,
        (match, prefix, middle, suffix) => {
          // Only fix if middle part doesn't look like it should end the string
          if (middle.trim() && !middle.includes(',') && !middle.includes('}') && !middle.includes(']')) {
            return `${prefix}\\"${middle}\\"${suffix}`;
          }
          return match;
        }
      );

      // Apply basic fixes
      cleanContent = this.fixCommonJsonIssues(cleanContent);

      console.log(`🔍 Strategy 4 cleaned content:`, cleanContent.substring(0, 200));
      return JSON.parse(cleanContent);
    } catch (error4) {
      console.warn('Strategy 4 failed:', error4);
    }

    // Strategy 5: Manual reconstruction for known patterns
    try {
      // Try to extract key-value pairs manually for routing responses
      if (content.includes('providerId') && content.includes('confidence')) {
        const providerMatch = content.match(/["']?providerId["']?\s*:\s*["']([^"']+)["']/);
        const confidenceMatch = content.match(/["']?confidence["']?\s*:\s*([0-9.]+)/);
        // More flexible reason matching that handles quotes
        const reasonMatch = content.match(/["']?reason["']?\s*:\s*["']([^"']*(?:\\"[^"']*)*[^"']*)["']/);

        if (providerMatch) {
          return {
            providerId: providerMatch[1],
            confidence: confidenceMatch ? parseFloat(confidenceMatch[1]) : 0.8,
            reason: reasonMatch ? reasonMatch[1].replace(/\\"/g, '"') : 'Manually reconstructed from malformed JSON'
          };
        }
      }

      // Try to extract classification patterns
      if (content.includes('isMultiRole') && content.includes('needsTools')) {
        const isMultiRoleMatch = content.match(/["']?isMultiRole["']?\s*:\s*(true|false)/);
        const needsToolsMatch = content.match(/["']?needsTools["']?\s*:\s*(true|false)/);
        const needsBrowsingMatch = content.match(/["']?needsBrowsing["']?\s*:\s*(true|false)/);

        return {
          isMultiRole: isMultiRoleMatch ? isMultiRoleMatch[1] === 'true' : false,
          roles: [{ roleId: 'fallback-role', confidence: 0.5, executionOrder: 1 }],
          needsBrowsing: needsBrowsingMatch ? needsBrowsingMatch[1] === 'true' : false,
          needsTools: needsToolsMatch ? needsToolsMatch[1] === 'true' : true,
          needsVision: false,
          toolTypes: ['google_docs'],
          reasoning: 'Manually reconstructed from malformed JSON'
        };
      }
    } catch (error3) {
      console.warn('Strategy 3 failed:', error3);
    }

    throw new Error('All JSON parsing strategies failed');
  }

  /**
   * Use Gemini AI for intelligent routing decisions
   */
  private async getIntelligentRoutingRecommendation(
    userMessage: string,
    classification: any,
    availableProviders: any[],
    availableTools: any[]
  ): Promise<{ recommendedProvider: string; confidence: number; reason: string }> {
    // Get all available API keys for fallback
    const apiKeys = this.getClassificationApiKeys();

    if (apiKeys.length === 0) {
      console.warn('No classification API keys available, falling back to basic routing');
      return {
        recommendedProvider: availableProviders[0]?.providerId || 'google',
        confidence: 0.5,
        reason: 'Fallback routing - no AI classification available'
      };
    }

    const systemPrompt = `You are RouKey's intelligent central router. Analyze the user request and classification data to select the best AI provider.

Available Providers:
${availableProviders.map(p => {
  const roleInfo = p.roles && p.roles.length > 0
    ? p.roles.map(r => `${r.name} (${r.description.substring(0, 100)})`).join('; ')
    : 'General Chat';
  return `- ${p.providerId}/${p.modelId} (Roles: ${roleInfo}, ID: ${p.id})`;
}).join('\n')}

Available Tools:
${availableTools.map(t => `- ${t.type} (Status: ${t.status})`).join('\n')}

Consider:
1. Task complexity and requirements
2. Provider capabilities and specializations
3. Connected role specializations and descriptions
4. Tool integration needs
5. Classification insights

Respond with JSON: {"providerId": "provider_id", "confidence": 0.95, "reason": "detailed explanation"}`;

      const userPrompt = `User Request: "${userMessage}"

Classification Data:
${JSON.stringify(classification, null, 2)}

Select the most appropriate provider for this request.`;

    // Try each API key until one works
    let lastError: Error | null = null;

    for (let i = 0; i < apiKeys.length; i++) {
      const currentApiKey = apiKeys[i];
      const keyLabel = i === 0 ? 'primary' : `fallback${i}`;

      try {
        console.log(`🔑 Attempting routing with ${keyLabel} key...`);

        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' + currentApiKey, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contents: [
            { role: 'user', parts: [{ text: systemPrompt + '\n\n' + userPrompt }] }
          ],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 500
          }
        })
      });

        if (!response.ok) {
          if (response.status === 429) {
            console.warn(`🚨 Rate limit hit on ${keyLabel} key for routing, trying next...`);
            lastError = new Error(`Rate limit on ${keyLabel} key`);
            continue; // Try next key
          } else {
            throw new Error(`Routing API error: ${response.status}`);
          }
        }

        const data = await response.json();
        const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

        if (content) {
          console.log(`✅ Routing successful with ${keyLabel} key`);

          try {
            console.log(`🔍 Attempting to parse routing JSON with robust parser`);
            const parsed = this.parseJsonRobustly(content);

            // Validate that the recommended provider exists
            const providerExists = availableProviders.some(p => p.providerId === parsed.providerId);

            if (providerExists) {
              return {
                recommendedProvider: parsed.providerId,
                confidence: Math.min(Math.max(parsed.confidence || 0.8, 0), 1),
                reason: parsed.reason || 'AI-based routing decision'
              };
            }
          } catch (parseError) {
            console.error(`❌ JSON parsing failed for routing with ${keyLabel} key:`, parseError);
            console.error(`Raw content:`, content.substring(0, 500));
            throw parseError;
          }
        }

      } catch (error) {
        console.error(`❌ Routing failed with ${keyLabel} key:`, error);
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // If this was the last key, we'll fall through to the fallback logic
        if (i === apiKeys.length - 1) {
          break;
        }

        // Otherwise, try the next key
        continue;
      }
    }

    // If we get here, all API keys failed
    console.error('🚨 All routing API keys failed, using basic fallback');
    console.error('Last error:', lastError);

    // Fallback to first available provider
    return {
      recommendedProvider: availableProviders[0]?.providerId || 'google',
      confidence: 0.6,
      reason: 'Fallback routing - AI routing failed'
    };
  }

  /**
   * Helper method to extract response text from nested objects
   */
  private extractResponseFromNestedObject(obj: any): string | null {
    if (!obj || typeof obj !== 'object') return null;

    // Direct response property
    if (obj.response && typeof obj.response === 'string') {
      console.log(`📤 Found response in obj.response`);
      return obj.response;
    }

    // Look for response in nested provider data
    if (obj.provider && obj.response) {
      console.log(`📤 Found response in provider object`);
      return obj.response;
    }

    // Check for finalOutput property
    if (obj.finalOutput && typeof obj.finalOutput === 'string') {
      console.log(`📤 Found response in obj.finalOutput`);
      return obj.finalOutput;
    }

    // Look for response in nested objects
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && typeof obj[key] === 'object') {
        const nestedResponse = this.extractResponseFromNestedObject(obj[key]);
        if (nestedResponse) {
          console.log(`📤 Found response in nested object: ${key}`);
          return nestedResponse;
        }
      }
    }

    console.log(`📤 No response found in object with keys: ${Object.keys(obj).join(', ')}`);
    return null;
  }

  /**
   * Execute Vision Node - Handles multimodal AI processing
   */
  private async executeVisionNode(
    node: WorkflowNode,
    input: any,
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[],
    onStreamChunk?: (chunk: string) => void
  ): Promise<any> {
    console.log(`👁️ Executing Vision Node: ${node.id}`);

    const config = node.data.config as any;

    if (!config.providerId || !config.modelId) {
      throw new Error('Vision node not properly configured - missing provider or model');
    }

    // Check if input contains images
    const hasImages = this.detectImagesInInput(input);

    if (!hasImages) {
      console.log(`⚠️ No images detected in input for vision node`);
      return {
        ...input,
        visionResult: {
          status: 'no_images',
          message: 'No images found in input'
        }
      };
    }

    // Get API key for the vision provider
    try {
      const apiKey = await this.getApiKeyForProvider(config.providerId);
      if (!apiKey) {
        throw new Error(`No API key available for provider: ${config.providerId}`);
      }

      // Extract text prompt and images from input
      const textPrompt = typeof input === 'string' ? input : input?.content || input?.prompt || 'Analyze this image';
      const images = this.extractImagesFromInput(input);

      // For vision processing, we'll use a multimodal prompt
      const visionPrompt = `${textPrompt}

Please analyze the provided image(s) and provide a detailed description of what you see.`;

      // Call the provider API for vision processing
      const visionResponse = await this.callProviderAPI(config.providerId, visionPrompt, config.modelId, apiKey, onStreamChunk);

      console.log(`👁️ Vision analysis completed using ${config.providerId}:${config.modelId}`);

      return {
        ...input,
        visionResult: {
          status: 'processed',
          analysis: visionResponse,
          provider: config.providerId,
          model: config.modelId,
          imageCount: images.length,
          timestamp: new Date().toISOString()
        },
        visionProcessed: true,
        response: visionResponse
      };

    } catch (error) {
      console.error(`❌ Vision processing error:`, error);
      return {
        ...input,
        visionResult: {
          status: 'error',
          message: `Vision processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          provider: config.providerId,
          model: config.modelId
        }
      };
    }
  }

  /**
   * Detect if input contains images
   */
  private detectImagesInInput(input: any): boolean {
    if (typeof input === 'string') {
      // Check for image URLs or base64 data
      return /\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(input) ||
             input.includes('data:image/') ||
             input.includes('base64,');
    }

    if (input && typeof input === 'object') {
      // Check for image properties
      return !!(input.images || input.image || input.imageUrl || input.imageData ||
               input.attachments?.some((att: any) => att.type === 'image'));
    }

    return false;
  }

  /**
   * Extract images from input
   */
  private extractImagesFromInput(input: any): string[] {
    const images: string[] = [];

    if (typeof input === 'string') {
      if (input.includes('data:image/') || /\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(input)) {
        images.push(input);
      }
    } else if (input && typeof input === 'object') {
      if (input.images && Array.isArray(input.images)) {
        images.push(...input.images);
      }
      if (input.image) {
        images.push(input.image);
      }
      if (input.imageUrl) {
        images.push(input.imageUrl);
      }
      if (input.imageData) {
        images.push(input.imageData);
      }
      if (input.attachments) {
        input.attachments.forEach((att: any) => {
          if (att.type === 'image' && att.url) {
            images.push(att.url);
          }
        });
      }
    }

    return images;
  }

  /**
   * Execute Role Agent Node - Handles role assignment and configuration
   */
  private async executeRoleAgentNode(
    node: WorkflowNode,
    input: any,
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[]
  ): Promise<any> {
    console.log(`👤 Executing Role Agent Node: ${node.id}`);

    const config = node.data.config as any;

    if (!config.roleName) {
      throw new Error('Role agent not configured - missing role name');
    }

    // Role agents don't execute directly - they provide role information to connected providers
    console.log(`👤 Role agent configured: ${config.roleName} (${config.roleType})`);

    return {
      ...input,
      assignedRole: {
        name: config.roleName,
        type: config.roleType,
        description: config.roleDescription,
        nodeId: node.id
      }
    };
  }

  /**
   * Get available roles from connected router node
   */
  private getAvailableRolesFromRouter(
    routerNode: WorkflowNode,
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[]
  ): Array<{ id: string; name: string; description: string }> {
    // Find all provider nodes connected to the router
    const providerConnections = edges.filter(edge =>
      edge.target === routerNode.id &&
      allNodes.find(n => n.id === edge.source)?.type === 'provider'
    );

    const roles: Array<{ id: string; name: string; description: string }> = [];

    // Get roles from connected providers
    providerConnections.forEach(connection => {
      const providerNode = allNodes.find(n => n.id === connection.source);
      if (providerNode) {
        // Find role agents connected to this provider
        const roleConnections = edges.filter(edge =>
          edge.target === providerNode.id &&
          edge.targetHandle === 'role' &&
          allNodes.find(n => n.id === edge.source)?.type === 'roleAgent'
        );

        roleConnections.forEach(roleConnection => {
          const roleNode = allNodes.find(n => n.id === roleConnection.source);
          if (roleNode) {
            const roleConfig = roleNode.data.config as any;
            roles.push({
              id: roleNode.id, // Use the node ID for routing
              name: roleConfig.roleName || roleNode.data.label || 'Unknown Role',
              description: roleConfig.roleDescription || roleConfig.customPrompt || 'General purpose AI assistant'
            });
          }
        });
      }
    });

    // If no specific roles found, add default roles
    if (roles.length === 0) {
      roles.push({
        id: 'general',
        name: 'General Assistant',
        description: 'General purpose AI assistant'
      });
    }

    return roles;
  }

  /**
   * Get available tools from the workflow
   */
  private getAvailableToolsFromWorkflow(
    allNodes: WorkflowNode[],
    edges: WorkflowEdge[]
  ): Array<{ id: string; name: string; type: string; status: string }> {
    const tools: Array<{ id: string; name: string; type: string; status: string }> = [];

    // Find all tool nodes in the workflow
    const toolNodes = allNodes.filter(node => node.type === 'tool');

    toolNodes.forEach(toolNode => {
      const config = toolNode.data.config as any;
      if (config?.toolType) {
        // Get tool display name from the toolType
        const toolDisplayNames: Record<string, string> = {
          google_drive: 'Google Drive',
          google_docs: 'Google Docs',
          google_sheets: 'Google Sheets',
          gmail: 'Gmail',
          calendar: 'Google Calendar',
          youtube: 'YouTube',
          notion: 'Notion',
          supabase: 'Supabase'
        };

        const toolName = toolDisplayNames[config.toolType] || config.toolType;

        // Check if tool is properly connected (both authenticated and connection status is connected)
        const isConnected = config.isAuthenticated && config.connectionStatus === 'connected';

        tools.push({
          id: toolNode.id,
          name: toolName,
          type: config.toolType,
          status: isConnected ? 'connected' : 'disconnected'
        });
      }
    });

    // Also check for browsing capability (if browsing nodes exist)
    const browsingNodes = allNodes.filter(node =>
      node.type === 'browsing' ||
      (node.type === 'tool' && node.data.config?.toolType === 'web_browsing')
    );

    if (browsingNodes.length > 0) {
      tools.push({
        id: 'web_browsing',
        name: 'Web Browsing',
        type: 'web_browsing',
        status: 'connected'
      });
    }

    return tools;
  }

  /**
   * Get classification API key with fallback support
   */
  private getClassificationApiKeys(): string[] {
    const keys = [
      process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY,
      process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY_FALLBACK1,
      process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY_FALLBACK2
    ].filter(Boolean) as string[];

    return keys;
  }

  /**
   * Perform comprehensive classification using Gemini with fallback keys
   */
  private async performComprehensiveClassification(
    prompt: string,
    availableRoles: Array<{ id: string; name: string; description: string }>,
    classificationApiKey: string,
    availableTools: Array<{ id: string; name: string; type: string; status: string }> = [],
    onStreamChunk?: (chunk: string) => void,
    conversationHistory?: Array<{ role: string; content: string }>
  ): Promise<any> {
    const roleInfoForPrompt = availableRoles.map(r =>
      `- Role ID: "${r.id}", Name: "${r.name}", Description: "${r.description}"`
    ).join('\n');

    const connectedTools = availableTools.filter(t => t.status === 'connected');
    const toolInfoForPrompt = connectedTools.length > 0
      ? connectedTools.map(t => `- ${t.name} (${t.type})`).join('\n')
      : 'No tools connected to this workflow';

    // Debug: Log connected tools for classification
    console.log(`🔧 Connected tools for classification:`, connectedTools);
    console.log(`📝 Tool info for prompt:`, toolInfoForPrompt);

    const currentDateTime = new Date().toISOString();
    const currentDate = new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const systemPrompt = `You are RouKey's Advanced Task Classifier. Current date/time: ${currentDateTime} (${currentDate})

Analyze the user request and determine:

1. ROLES NEEDED: Which specialized roles should handle this task
2. BROWSING REQUIRED: Does this need web browsing/research?
3. TOOLS REQUIRED: Does this need external tools?
4. VISION REQUIRED: Does this involve image analysis?
5. MULTI-ROLE: Does this require coordination between multiple capabilities?

IMPORTANT: Consider the conversation context. If previous messages mentioned using a specific tool (like Google Docs), and the current request is a follow-up or clarification, you should still detect the tool requirement.

Available Roles:
${roleInfoForPrompt}

Connected Tools Available:
${toolInfoForPrompt}

BROWSING DETECTION RULES:
- TEMPORAL QUESTIONS: Any question about "when", "latest", "current", "recent", "today", "yesterday", "tomorrow", "next", "last", "this week/month/year" requires browsing for current data
- REAL-TIME DATA: Stock prices, weather, news, sports schedules, current events
- RESEARCH TASKS: "Find information about", "research", "look up", "search for"
- VERIFICATION: "Is this still true?", "What's the current status?"

VISION DETECTION RULES:
- Direct mentions: "analyze image", "describe picture", "what's in this photo"
- Image processing: "OCR", "read text from image", "extract data from screenshot"
- Visual analysis: "identify objects", "detect faces", "analyze chart/graph"
- File references: mentions of image files (.jpg, .png, etc.)

TOOLS DETECTION RULES:
- CRITICAL: Only suggest tools that are CONNECTED to this workflow (status: connected)
- If user mentions a tool by name that is connected, set needsTools=true and include it in toolTypes
- Google Docs: "create document", "write report", "save to docs", "write in google docs", "use google docs"
- Google Drive: "save file", "upload to drive", "store in drive", "access my files"
- Google Sheets: "create spreadsheet", "analyze data", "make table", "save to sheets"
- Gmail: "send email", "compose message", "email someone"
- Google Calendar: "schedule meeting", "create event", "check calendar"
- YouTube: "upload video", "manage channel", "youtube analytics"
- Notion: "create page", "update database", "save to notion"
- Supabase: "database query", "store data", "retrieve records"
- IMPORTANT: If user explicitly mentions any connected tool, always set needsTools=true

MULTI-ROLE SCENARIOS:
- Different specialized roles needed (research + writing + coding)
- Tools + Browsing combinations (research online + save to docs)
- Sequential workflows (browse → analyze → create document)
- Complex tasks requiring multiple capabilities

CRITICAL: You MUST respond with ONLY valid JSON. Do not include any explanatory text, markdown formatting, or conversational responses.

Respond with this exact JSON structure:
{
  "isMultiRole": boolean,
  "roles": [{"roleId": "role_id", "confidence": 0.9, "executionOrder": 1}],
  "needsBrowsing": boolean,
  "needsTools": boolean,
  "needsVision": boolean,
  "toolTypes": ["tool_name_from_connected_list"],
  "reasoning": "detailed explanation of classification decisions"
}

Examples:
- "when does messi play his next match?" → browsing (temporal question requiring current sports schedule)
- "what's the latest AI news?" → browsing (temporal + current events)
- "write a fairy tale" → single role, no browsing/tools
- "research AI trends and create a report" → multi-role (research + writing), browsing + docs tool
- "analyze this screenshot and save findings to spreadsheet" → multi-role, vision + sheets tool
- "find current stock price of AAPL" → browsing (real-time financial data)
- "create a todo list" → single role, no browsing (unless sheets tool connected)
- "what happened in the news yesterday?" → browsing (temporal question)
- "code a calculator and document the API" → multi-role (coding + documentation)
- "write a story in google docs" → needsTools=true, toolTypes=["google_docs"] (if google_docs is connected)
- "save this to my drive" → needsTools=true, toolTypes=["google_drive"] (if google_drive is connected)
- "use google docs to create a document" → needsTools=true, toolTypes=["google_docs"] (if connected)
- "you have access to my google docs, do it for me" → needsTools=true, toolTypes=["google_docs"] (if connected)`;

    // Get all available API keys for fallback
    const apiKeys = this.getClassificationApiKeys();
    let lastError: Error | null = null;

    // Try each API key until one works
    for (let i = 0; i < apiKeys.length; i++) {
      const currentApiKey = apiKeys[i];
      const keyLabel = i === 0 ? 'primary' : `fallback${i}`;

      try {
        console.log(`🔑 Attempting classification with ${keyLabel} key...`);

        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${currentApiKey}`,
            'Connection': 'keep-alive',
            'User-Agent': 'RoKey/1.0 (Workflow-Classification)',
          },
        body: JSON.stringify({
          model: 'gemini-2.0-flash-lite',
          messages: [
            { role: 'system', content: systemPrompt + '\n\nIMPORTANT: You MUST respond with ONLY valid JSON. Do not include any explanatory text, markdown, or conversational responses. Start your response with { and end with }.' },
            ...(conversationHistory && conversationHistory.length > 0
              ? [
                  { role: 'system', content: `Previous conversation context:\n${conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')}` },
                  { role: 'user', content: `Current task: ${prompt}\n\nRespond with ONLY JSON:` }
                ]
              : [{ role: 'user', content: `Task: ${prompt}` }]
            )
          ],
          temperature: 0.1,
          max_tokens: 500,
          stream: false // Classifier should not stream - runs silently
        })
      });

        if (!response.ok) {
          if (response.status === 429) {
            console.warn(`🚨 Rate limit hit on ${keyLabel} key, trying next...`);
            lastError = new Error(`Rate limit on ${keyLabel} key`);
            continue; // Try next key
          } else {
            throw new Error(`Classification API error: ${response.status}`);
          }
        }

        // Classification always uses non-streaming mode (runs silently)
        const result = await response.json();
        const content = result.choices?.[0]?.message?.content || '';

        if (!content) {
          throw new Error('No classification result received');
        }

        console.log(`✅ Classification successful with ${keyLabel} key`);

        // Success! Process the result
        // Clean the content to remove markdown code blocks if present
        let cleanContent = content.trim();
        if (cleanContent.startsWith('```json')) {
          cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        // Try to extract JSON from conversational response
        const jsonMatch = cleanContent.match(/\{[\s\S]*?\}(?=\s*$|\s*[^}]*$)/);
        if (jsonMatch) {
          cleanContent = jsonMatch[0];
        } else {
          // If no JSON found, the API returned a conversational response
          console.warn('🚨 Classification API returned conversational response instead of JSON:', cleanContent.substring(0, 100));
          throw new Error('Classification API returned non-JSON response');
        }

        console.log(`🔍 Attempting to parse classification JSON with robust parser`);
        const parsed = this.parseJsonRobustly(cleanContent);

        // Validate and filter roles against available roles
        if (parsed.roles) {
          parsed.roles = parsed.roles.filter((role: any) =>
            availableRoles.some(availableRole => availableRole.id === role.roleId)
          );
        }

        return parsed;

      } catch (error) {
        console.error(`❌ Classification failed with ${keyLabel} key:`, error);
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // If this was the last key, we'll fall through to the fallback logic
        if (i === apiKeys.length - 1) {
          break;
        }

        // Otherwise, try the next key
        continue;
      }
    }

    // If we get here, all API keys failed
    console.error('🚨 All classification API keys failed, using smart fallback');
    console.error('Last error:', lastError);

    // Smart fallback classification
      const lowerPrompt = prompt.toLowerCase();

      // Detect temporal questions for browsing
      const temporalKeywords = ['when', 'latest', 'current', 'recent', 'today', 'yesterday', 'tomorrow', 'next', 'last', 'this week', 'this month', 'this year', 'now'];
      const needsBrowsing = temporalKeywords.some(keyword => lowerPrompt.includes(keyword)) ||
                           lowerPrompt.includes('search') ||
                           lowerPrompt.includes('find') ||
                           lowerPrompt.includes('research') ||
                           lowerPrompt.includes('look up');

      // Detect vision requirements
      const visionKeywords = ['image', 'photo', 'picture', 'screenshot', 'analyze', 'ocr', 'visual', 'chart', 'graph'];
      const needsVision = visionKeywords.some(keyword => lowerPrompt.includes(keyword));

      // Detect tool requirements based on connected tools
      const connectedToolTypes = availableTools.filter(t => t.status === 'connected').map(t => t.type);
      const needsTools = connectedToolTypes.length > 0 && (
        lowerPrompt.includes('document') ||
        lowerPrompt.includes('spreadsheet') ||
        lowerPrompt.includes('save') ||
        lowerPrompt.includes('create report') ||
        lowerPrompt.includes('google docs') ||
        lowerPrompt.includes('google doc') ||
        lowerPrompt.includes('write') ||
        lowerPrompt.includes('story') ||
        lowerPrompt.includes('docs')
      );

      return {
        isMultiRole: false,
        roles: [{ roleId: availableRoles[0]?.id || 'general', confidence: 0.5, executionOrder: 1 }],
        needsBrowsing,
        needsTools,
        needsVision,
        toolTypes: needsTools ? connectedToolTypes : [],
        reasoning: 'Fallback classification with smart detection due to API error'
      };
  }

  /**
   * Detect if input contains images for vision processing
   */
  private detectImagesInInput(input: any): boolean {
    if (typeof input === 'string') return false;

    // Check for image URLs or base64 images in various input formats
    const inputStr = JSON.stringify(input).toLowerCase();
    return inputStr.includes('image_url') ||
           inputStr.includes('data:image') ||
           inputStr.includes('.jpg') ||
           inputStr.includes('.png') ||
           inputStr.includes('.jpeg') ||
           inputStr.includes('.gif') ||
           inputStr.includes('.webp');
  }
}

// Export singleton instance
export const workflowExecutor = WorkflowExecutor.getInstance();
