// Function Call Handler
// Processes AI model function calls and executes the appropriate tools

import { getFunctionByName, TOOL_FUNCTION_MAP } from './functionSchemas';
import {
  GoogleDriveAPI,
  GoogleDocsAPI,
  GoogleSheetsAPI,
  GmailAPI
} from './toolImplementations';
import {
  GoogleCalendarAPI,
  YouTubeAPI,
  NotionAPI
} from './toolImplementations2';
import { WebBrowsingTool } from '@/lib/tools/webBrowsing';

export interface FunctionCall {
  name: string;
  arguments: string | Record<string, any>;
}

export interface FunctionCallResult {
  success: boolean;
  result?: any;
  error?: string;
  functionName: string;
  executionTime: number;
}

export class FunctionCallHandler {
  private userId: string;
  private timeout: number;

  constructor(userId: string, timeout: number = 600000) {
    this.userId = userId;
    this.timeout = timeout;
  }

  /**
   * Execute a function call from AI model with comprehensive error handling
   */
  async executeFunctionCall(functionCall: FunctionCall): Promise<FunctionCallResult> {
    const startTime = Date.now();

    try {
      // Validate function call structure
      if (!functionCall || !functionCall.name) {
        throw new Error('Invalid function call: missing function name');
      }

      // Parse arguments if they're a string
      let args: Record<string, any>;
      if (typeof functionCall.arguments === 'string') {
        try {
          args = JSON.parse(functionCall.arguments);
        } catch (e) {
          throw new Error(`Invalid function arguments JSON: ${functionCall.arguments}. Error: ${e instanceof Error ? e.message : 'Unknown JSON parse error'}`);
        }
      } else if (typeof functionCall.arguments === 'object' && functionCall.arguments !== null) {
        args = functionCall.arguments;
      } else {
        throw new Error(`Invalid function arguments type: ${typeof functionCall.arguments}`);
      }

      // Validate function exists
      const functionSchema = getFunctionByName(functionCall.name);
      if (!functionSchema) {
        throw new Error(`Unknown function: ${functionCall.name}. Available functions: ${this.getAvailableFunctionNames().join(', ')}`);
      }

      // Validate required parameters
      const missingParams = functionSchema.parameters.required.filter(param => !(param in args));
      if (missingParams.length > 0) {
        throw new Error(`Missing required parameters: ${missingParams.join(', ')}. Provided: ${Object.keys(args).join(', ')}`);
      }

      // Validate parameter types (basic validation)
      this.validateParameterTypes(args, functionSchema);

      console.log(`🔧 Executing function: ${functionCall.name} with args:`, JSON.stringify(args, null, 2));

      // Execute the appropriate function with timeout
      const result = await Promise.race([
        this.executeFunction(functionCall.name, args),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Function ${functionCall.name} timed out after ${this.timeout}ms`)), this.timeout)
        )
      ]);

      console.log(`✅ Function ${functionCall.name} completed successfully in ${Date.now() - startTime}ms`);

      return {
        success: true,
        result,
        functionName: functionCall.name,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Function call error for ${functionCall.name}:`, errorMessage);

      // Log additional context for debugging
      console.error(`Function call context:`, {
        functionName: functionCall.name,
        arguments: functionCall.arguments,
        userId: this.userId,
        timeout: this.timeout,
        executionTime: Date.now() - startTime
      });

      return {
        success: false,
        error: errorMessage,
        functionName: functionCall.name,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Get list of available function names for error messages
   */
  private getAvailableFunctionNames(): string[] {
    const allFunctions = Object.values(TOOL_FUNCTION_MAP).flat();
    return allFunctions.map(func => func.name);
  }

  /**
   * Basic parameter type validation
   */
  private validateParameterTypes(args: Record<string, any>, schema: any): void {
    for (const [paramName, paramValue] of Object.entries(args)) {
      const paramSchema = schema.parameters.properties[paramName];
      if (!paramSchema) continue; // Skip validation for unknown parameters

      const expectedType = paramSchema.type;
      const actualType = Array.isArray(paramValue) ? 'array' : typeof paramValue;

      if (expectedType === 'string' && actualType !== 'string') {
        throw new Error(`Parameter '${paramName}' must be a string, got ${actualType}`);
      } else if (expectedType === 'number' && actualType !== 'number') {
        throw new Error(`Parameter '${paramName}' must be a number, got ${actualType}`);
      } else if (expectedType === 'array' && !Array.isArray(paramValue)) {
        throw new Error(`Parameter '${paramName}' must be an array, got ${actualType}`);
      }
    }
  }

  /**
   * Execute multiple function calls with error isolation and retry logic
   */
  async executeFunctionCalls(functionCalls: FunctionCall[]): Promise<FunctionCallResult[]> {
    if (!functionCalls || functionCalls.length === 0) {
      return [];
    }

    console.log(`🔧 Executing ${functionCalls.length} function calls:`, functionCalls.map(fc => fc.name));

    // Execute function calls with error isolation
    const results: FunctionCallResult[] = [];

    for (const functionCall of functionCalls) {
      try {
        const result = await this.executeFunctionCallWithRetry(functionCall);
        results.push(result);
      } catch (error) {
        // Even if individual function fails, continue with others
        console.error(`Failed to execute function ${functionCall.name}:`, error);
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          functionName: functionCall.name,
          executionTime: 0
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`✅ Function execution completed: ${successCount}/${results.length} successful`);

    return results;
  }

  /**
   * Execute function call with retry logic
   */
  private async executeFunctionCallWithRetry(functionCall: FunctionCall, maxRetries: number = 2): Promise<FunctionCallResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        const result = await this.executeFunctionCall(functionCall);

        // If successful, return immediately
        if (result.success) {
          if (attempt > 1) {
            console.log(`✅ Function ${functionCall.name} succeeded on attempt ${attempt}`);
          }
          return result;
        }

        // If not successful but no exception, treat as final result
        if (attempt === maxRetries + 1) {
          return result;
        }

        // Prepare for retry
        lastError = new Error(result.error || 'Function execution failed');
        console.warn(`⚠️ Function ${functionCall.name} failed on attempt ${attempt}, retrying...`);

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt === maxRetries + 1) {
          throw lastError;
        }

        console.warn(`⚠️ Function ${functionCall.name} threw error on attempt ${attempt}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
      }
    }

    throw lastError || new Error('Function execution failed after all retries');
  }

  /**
   * Route function call to appropriate tool implementation
   */
  private async executeFunction(functionName: string, args: Record<string, any>): Promise<any> {
    switch (functionName) {
      // Google Docs Functions
      case 'create_google_document':
        return await this.createGoogleDocument(args);
      case 'update_google_document':
        return await this.updateGoogleDocument(args);
      case 'get_google_document':
        return await GoogleDocsAPI.getDocument(this.userId, args, this.timeout);
      case 'list_google_documents':
        return await this.listGoogleDocuments(args);

      // Google Drive Functions
      case 'list_google_drive_files':
        return await GoogleDriveAPI.listFiles(this.userId, args, this.timeout);
      case 'search_google_drive_files':
        return await GoogleDriveAPI.searchFiles(this.userId, args, this.timeout);
      case 'create_google_drive_file':
        return await GoogleDriveAPI.createFile(this.userId, args, this.timeout);

      // Google Sheets Functions
      case 'create_google_spreadsheet':
        return await GoogleSheetsAPI.createSpreadsheet(this.userId, args, this.timeout);
      case 'update_google_sheets_cells':
        return await GoogleSheetsAPI.updateCells(this.userId, args, this.timeout);
      case 'read_google_sheets_range':
        return await GoogleSheetsAPI.readRange(this.userId, args, this.timeout);

      // Gmail Functions
      case 'send_gmail_email':
        return await GmailAPI.sendEmail(this.userId, args, this.timeout);
      case 'list_gmail_emails':
        return await GmailAPI.listEmails(this.userId, args, this.timeout);
      case 'search_gmail_emails':
        return await GmailAPI.searchEmails(this.userId, args, this.timeout);

      // Google Calendar Functions
      case 'create_calendar_event':
        return await GoogleCalendarAPI.createEvent(this.userId, args, this.timeout);
      case 'list_calendar_events':
        return await GoogleCalendarAPI.listEvents(this.userId, args, this.timeout);

      // YouTube Functions
      case 'search_youtube_videos':
        return await YouTubeAPI.searchVideos(this.userId, args, this.timeout);
      case 'get_youtube_video_details':
        return await YouTubeAPI.getVideoDetails(this.userId, args, this.timeout);

      // Notion Functions
      case 'create_notion_page':
        return await this.createNotionPage(args);
      case 'update_notion_page':
        return await this.updateNotionPage(args);
      case 'search_notion_pages':
        return await NotionAPI.queryDatabase(this.userId, args, this.timeout);

      // Web Browsing Functions
      case 'search_web':
        return await this.searchWeb(args);
      case 'navigate_to_url':
        return await this.navigateToUrl(args);
      case 'take_screenshot':
        return await this.takeScreenshot(args);
      case 'execute_custom_browsing':
        return await this.executeCustomBrowsing(args);

      default:
        throw new Error(`Function not implemented: ${functionName}`);
    }
  }

  /**
   * Enhanced Google Docs creation with content support
   */
  private async createGoogleDocument(args: Record<string, any>): Promise<any> {
    const { title, content } = args;
    
    // Create the document
    const createResult = await GoogleDocsAPI.createDocument(this.userId, { title }, this.timeout);
    
    if (!createResult.success || !createResult.document) {
      return createResult;
    }

    const documentId = createResult.document.documentId;
    const documentUrl = `https://docs.google.com/document/d/${documentId}/edit`;

    // Add content if provided
    if (content) {
      const updateRequests = [
        {
          insertText: {
            location: { index: 1 },
            text: content
          }
        }
      ];

      const updateResult = await GoogleDocsAPI.updateDocument(this.userId, {
        documentId,
        requests: updateRequests
      }, this.timeout);

      if (!updateResult.success) {
        console.warn('Document created but content update failed:', updateResult.error);
      }
    }

    return {
      success: true,
      document: createResult.document,
      documentUrl,
      title,
      content: content || '',
      message: `Successfully created Google Doc: "${title}"`
    };
  }

  /**
   * Enhanced Google Docs update with flexible content insertion
   */
  private async updateGoogleDocument(args: Record<string, any>): Promise<any> {
    const { documentId, content, insertIndex = 1 } = args;

    const updateRequests = [
      {
        insertText: {
          location: { index: insertIndex },
          text: content
        }
      }
    ];

    const result = await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests: updateRequests
    }, this.timeout);

    if (result.success) {
      return {
        ...result,
        documentUrl: `https://docs.google.com/document/d/${documentId}/edit`,
        message: 'Document updated successfully'
      };
    }

    return result;
  }

  /**
   * List Google Documents using Google Drive API
   */
  private async listGoogleDocuments(args: Record<string, any>): Promise<any> {
    const { query, maxResults = 20 } = args;

    // Use Google Drive API to list documents
    const searchParams: any = {
      mimeType: 'application/vnd.google-apps.document',
      maxResults
    };

    if (query) {
      searchParams.query = `name contains '${query}'`;
    }

    try {
      const { GoogleDriveAPI } = await import('./toolImplementations');
      const result = await GoogleDriveAPI.searchFiles(this.userId, searchParams, this.timeout);

      if (result.success && result.files) {
        // Format the results to include document URLs
        const documents = result.files.map((file: any) => ({
          id: file.id,
          name: file.name,
          url: `https://docs.google.com/document/d/${file.id}/edit`,
          createdTime: file.createdTime,
          modifiedTime: file.modifiedTime
        }));

        return {
          success: true,
          documents,
          message: `Found ${documents.length} Google Documents`
        };
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: `Failed to list Google Documents: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Enhanced Notion page creation
   */
  private async createNotionPage(args: Record<string, any>): Promise<any> {
    const { title, content, parentPageId } = args;

    const pageData = {
      parent: parentPageId 
        ? { type: 'page_id', page_id: parentPageId }
        : { type: 'page_id', page_id: 'default' }, // This would need proper configuration
      properties: {
        title: {
          title: [{ text: { content: title } }]
        }
      },
      ...(content && {
        children: [
          {
            object: 'block',
            type: 'paragraph',
            paragraph: {
              rich_text: [{ text: { content } }]
            }
          }
        ]
      })
    };

    const result = await NotionAPI.createPage(this.userId, pageData, this.timeout);

    if (result.success && result.page) {
      return {
        ...result,
        pageUrl: result.page.url,
        title,
        message: `Successfully created Notion page: "${title}"`
      };
    }

    return result;
  }

  /**
   * Enhanced Notion page update
   */
  private async updateNotionPage(args: Record<string, any>): Promise<any> {
    const { pageId, content } = args;

    // This would need to be implemented based on Notion's block update API
    // For now, return a placeholder
    return {
      success: false,
      error: 'Notion page update not yet fully implemented'
    };
  }

  /**
   * Web Browsing Functions
   */
  private async searchWeb(args: Record<string, any>): Promise<any> {
    const { query, searchEngine = 'google', maxResults = 10 } = args;

    const result = await WebBrowsingTool.search(query, {
      searchEngine,
      extractionType: 'search'
    });

    if (!result.success) {
      throw new Error(`Web search failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      results: result.data,
      query,
      searchEngine,
      timestamp: result.timestamp
    };
  }

  private async navigateToUrl(args: Record<string, any>): Promise<any> {
    const { url, extractionType = 'content', customSelector } = args;

    const result = await WebBrowsingTool.navigate(url, {
      extractionType,
      customSelector
    });

    if (!result.success) {
      throw new Error(`Navigation failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      content: result.data,
      url,
      extractionType,
      timestamp: result.timestamp
    };
  }

  private async takeScreenshot(args: Record<string, any>): Promise<any> {
    const { url, fullPage = false } = args;

    const result = await WebBrowsingTool.takeScreenshot(url, {
      extractionType: 'screenshot'
    });

    if (!result.success) {
      throw new Error(`Screenshot failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      screenshot: result.data,
      url,
      fullPage,
      timestamp: result.timestamp
    };
  }

  private async executeCustomBrowsing(args: Record<string, any>): Promise<any> {
    const { url, code, context = {} } = args;

    const result = await WebBrowsingTool.executeCustom(url, code, context, {
      extractionType: 'custom'
    });

    if (!result.success) {
      throw new Error(`Custom browsing failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      result: result.data,
      url,
      code,
      context,
      timestamp: result.timestamp
    };
  }
}

/**
 * Format function call results for AI model consumption
 */
export function formatFunctionCallResults(results: FunctionCallResult[]): string {
  return results.map(result => {
    if (result.success) {
      return `Function ${result.functionName} executed successfully in ${result.executionTime}ms:\n${JSON.stringify(result.result, null, 2)}`;
    } else {
      return `Function ${result.functionName} failed after ${result.executionTime}ms: ${result.error}`;
    }
  }).join('\n\n');
}

/**
 * Check if a response contains function calls (OpenAI format)
 */
export function hasFunctionCalls(response: any): boolean {
  return response?.choices?.[0]?.message?.tool_calls?.length > 0 ||
         response?.choices?.[0]?.message?.function_call;
}

/**
 * Extract function calls from AI response (OpenAI format)
 */
export function extractFunctionCalls(response: any): FunctionCall[] {
  const message = response?.choices?.[0]?.message;
  const functionCalls: FunctionCall[] = [];

  // Handle new tool_calls format
  if (message?.tool_calls) {
    for (const toolCall of message.tool_calls) {
      if (toolCall.type === 'function') {
        functionCalls.push({
          name: toolCall.function.name,
          arguments: toolCall.function.arguments
        });
      }
    }
  }

  // Handle legacy function_call format
  if (message?.function_call) {
    functionCalls.push({
      name: message.function_call.name,
      arguments: message.function_call.arguments
    });
  }

  return functionCalls;
}
